import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import type { Courses, Organization, UserCourses } from "@repo/database";

type CourseWithOrganization = Courses & {
	organization: Organization;
};

type UserCourseWithCourse = UserCourses & {
	course: CourseWithOrganization;
};

export async function GET(request: NextRequest) {
	try {
		const session = await getSession();

		if (!session?.user) {
			return NextResponse.json(
				{ error: "Unauthorized" },
				{ status: 401 },
			);
		}

		const { searchParams } = new URL(request.url);
		const organizationSlug = searchParams.get("organizationSlug");
		const isAdmin = session.user.role === "admin";

		let formattedCourses;

		if (isAdmin) {
			// Admin users see all courses
			const allCourses = (await db.courses.findMany({
				where: {
					...(organizationSlug && {
						organization: {
							slug: organizationSlug,
						},
					}),
				},
				include: {
					organization: true,
				},
				orderBy: {
					createdAt: "desc",
				},
			})) as CourseWithOrganization[];

			formattedCourses = allCourses.map(
				(course: CourseWithOrganization) => ({
					id: course.id,
					title: course.name,
					description:
						course.description || "Curso disponível na plataforma",
					type: "course" as const,
					image: course.logo,
					isAccessible: true,
					organizationSlug: course.organization.slug,
					courseId: course.id,
					finalTime: null,
					createdAt: course.createdAt.toISOString(),
					updatedAt: course.updatedAt.toISOString(),
				}),
			);
		} else {
			// Regular users see only courses they have access to
			const userCourses = (await db.userCourses.findMany({
				where: {
					userId: session.user.id,
					...(organizationSlug && {
						course: {
							organization: {
								slug: organizationSlug,
							},
						},
					}),
				},
				include: {
					course: {
						include: {
							organization: true,
						},
					},
				},
				orderBy: {
					createdAt: "desc",
				},
			})) as UserCourseWithCourse[];

			formattedCourses = userCourses.map(
				(userCourse: UserCourseWithCourse) => ({
					id: userCourse.course.id,
					title: userCourse.course.name,
					description:
						userCourse.course.description ||
						"Curso disponível na plataforma",
					type: "course" as const,
					image: userCourse.course.logo,
					isAccessible: true,
					organizationSlug: userCourse.course.organization.slug,
					courseId: userCourse.courseId,
					finalTime: userCourse.finalTime?.toISOString(),
					createdAt: userCourse.createdAt.toISOString(),
					updatedAt: userCourse.updatedAt.toISOString(),
				}),
			);
		}

		return NextResponse.json({
			data: formattedCourses,
			total: formattedCourses.length,
		});
	} catch (error) {
		console.error("Error fetching user courses:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 },
		);
	}
}
