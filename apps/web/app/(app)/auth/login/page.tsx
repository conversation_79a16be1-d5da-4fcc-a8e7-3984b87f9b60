import { LoginForm } from "@saas/auth/components/LoginForm";
import { Logo } from "@shared/components/Logo";
import { getTranslations } from "next-intl/server";
import Link from "next/link";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("auth.login.title"),
	};
}

export default function LoginPage() {
	return (
		<div className="grid min-h-svh lg:grid-cols-2">

			<div className="relative hidden lg:block overflow-hidden">
				<img
					src="/images/banner1.jpg"
					alt="Cakto Members"
					className="absolute inset-0 h-full w-full object-cover"
				/>
				<div className="absolute inset-0 bg-black/40" />
				<div className="absolute inset-0 opacity-20 bg-gradient-to-br from-primary/20 to-primary/40" />
			</div>


			<div className="flex flex-col gap-4 p-6 md:p-10">

				<div className="flex justify-center gap-2 md:justify-start">
					<Link href="/">
						<Logo withLabel={true} />
					</Link>
				</div>


				<div className="flex flex-1 items-center justify-center">
					<div className="w-full lg:w-[400px] mx-auto">
						<LoginForm />
					</div>
				</div>
			</div>
		</div>
	);
}
