"use client";

import { useCallback, useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Progress } from "@ui/components/progress";
import { Badge } from "@ui/components/badge";
import {
	UploadIcon,
	ImageIcon,
	XIcon,
	LoaderIcon,
	AlertCircleIcon,
} from "lucide-react";
import { useImageUpload } from "../hooks/useImageUpload";

interface ImageUploadProps {
	value?: string;
	onChange: (url: string) => void;
	organizationId: string;
	fileType: "course-logo" | "module-cover" | "lesson-cover";
	label?: string;
	description?: string;
	className?: string;
}

export function ImageUpload({
	value,
	onChange,
	organizationId,
	fileType,
	label = "Upload de Imagem",
	description = "Selecione uma imagem para fazer upload",
	className = "",
}: ImageUploadProps) {
	const [isDragOver, setIsDragOver] = useState(false);
	const { uploadImage, isUploading, uploadProgress, error, resetUpload } = useImageUpload({
		organizationId,
		fileType,
	});

	const handleFileSelect = useCallback(
		async (file: File) => {
			if (!file.type.startsWith("image/")) {
				return;
			}

			const uploadedUrl = await uploadImage(file);
			if (uploadedUrl) {
				onChange(uploadedUrl);
			}
		},
		[uploadImage, onChange]
	);

	const handleFileChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			const file = event.target.files?.[0];
			if (file) {
				handleFileSelect(file);
			}
		},
		[handleFileSelect]
	);

	const handleDrop = useCallback(
		(event: React.DragEvent<HTMLDivElement>) => {
			event.preventDefault();
			setIsDragOver(false);

			const file = event.dataTransfer.files[0];
			if (file) {
				handleFileSelect(file);
			}
		},
		[handleFileSelect]
	);

	const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		setIsDragOver(true);
	}, []);

	const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		setIsDragOver(false);
	}, []);

	const handleRemove = useCallback(() => {
		onChange("");
		resetUpload();
	}, [onChange, resetUpload]);

	// Check if organization is selected
	const isOrganizationSelected = organizationId && organizationId.trim() !== "";

	return (
		<div className={`space-y-3 ${className}`}>
			<label className="text-sm font-medium">{label}</label>

			{/* Current Image Preview */}
			{value && !isUploading && (
				<div className="relative inline-block">
					<img
						src={value}
						alt="Preview"
						className="h-24 w-24 rounded-lg object-cover border"
					/>
					<Button
						type="button"
						variant="destructive"
						size="sm"
						className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full"
						onClick={handleRemove}
					>
						<XIcon className="h-3 w-3" />
					</Button>
				</div>
			)}

			{/* Upload Area */}
			{!value && (
				<div
					className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
						isDragOver
							? "border-primary bg-primary/5"
							: "border-muted-foreground/25 hover:border-muted-foreground/50"
					} ${!isOrganizationSelected ? "opacity-50 cursor-not-allowed" : ""}`}
					onDrop={isOrganizationSelected ? handleDrop : undefined}
					onDragOver={isOrganizationSelected ? handleDragOver : undefined}
					onDragLeave={isOrganizationSelected ? handleDragLeave : undefined}
				>
					{!isOrganizationSelected ? (
						<div className="space-y-2">
							<AlertCircleIcon className="mx-auto h-8 w-8 text-muted-foreground" />
							<p className="text-sm text-muted-foreground">
								Selecione um workspace primeiro para fazer upload da imagem
							</p>
						</div>
					) : (
						<>
							<ImageIcon className="mx-auto h-8 w-8 text-muted-foreground" />
							<p className="text-sm text-muted-foreground mt-2">{description}</p>
							<p className="text-xs text-muted-foreground mt-1">
								Arraste uma imagem aqui ou clique para selecionar
							</p>
							<Input
								type="file"
								accept="image/*"
								onChange={handleFileChange}
								className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
								disabled={!isOrganizationSelected}
							/>
						</>
					)}
				</div>
			)}

			{/* Upload Progress */}
			{isUploading && (
				<div className="space-y-2">
					<div className="flex items-center justify-between text-sm">
						<span>Fazendo upload...</span>
						<span>{uploadProgress}%</span>
					</div>
					<Progress value={uploadProgress} className="w-full" />
				</div>
			)}

			{/* Error Message */}
			{error && (
				<div className="flex items-center gap-2 text-sm text-destructive">
					<AlertCircleIcon className="h-4 w-4" />
					<span>{error}</span>
				</div>
			)}

			{/* Upload Button (Alternative) */}
			{!value && isOrganizationSelected && !isUploading && (
				<div className="flex justify-center">
					<Button
						type="button"
						variant="outline"
						onClick={() => {
							const input = document.createElement("input");
							input.type = "file";
							input.accept = "image/*";
							input.onchange = (e) => {
								const file = (e.target as HTMLInputElement).files?.[0];
								if (file) {
									handleFileSelect(file);
								}
							};
							input.click();
						}}
						className="flex items-center gap-2"
					>
						<UploadIcon className="h-4 w-4" />
						Selecionar Imagem
					</Button>
				</div>
			)}
		</div>
	);
}
