"use client";

import { useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
	SettingsIcon,
	LinkIcon,
	UsersIcon,
} from "lucide-react";
import type { CourseFormData } from "../types";

interface CourseSettingsFormProps {
	data: CourseFormData;
	onUpdate: (data: Partial<CourseFormData>) => void;
	onNext: () => void;
	onPrevious: () => void;
}

export function CourseSettingsForm({ data, onUpdate }: CourseSettingsFormProps) {
	const handleFieldChange = useCallback((field: keyof CourseFormData, value: any) => {
		onUpdate({ [field]: value });
	}, [onUpdate]);

	return (
		<div className="max-w-4xl mx-auto space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<SettingsIcon className="h-5 w-5 text-primary" />
						Configurações do Curso
					</CardTitle>
					<p className="text-muted-foreground">
						Configure as informações adicionais do seu curso
					</p>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Comunidade */}
					<div className="space-y-4">
						<h3 className="font-semibold text-foreground flex items-center gap-2">
							<UsersIcon className="h-4 w-4" />
							Comunidade
						</h3>
						<div className="space-y-2">
							<Input
								placeholder="Nome da comunidade relacionada"
								value={data.community || ""}
								onChange={(e) => handleFieldChange("community", e.target.value)}
							/>
							<p className="text-sm text-muted-foreground">
								Nome da comunidade ou grupo relacionado ao curso (opcional)
							</p>
						</div>
					</div>

					{/* Link da Comunidade */}
					<div className="space-y-4">
						<h3 className="font-semibold text-foreground flex items-center gap-2">
							<LinkIcon className="h-4 w-4" />
							Link da Comunidade
						</h3>
						<div className="space-y-2">
							<Input
								placeholder="https://discord.gg/comunidade ou https://t.me/grupo"
								value={data.link || ""}
								onChange={(e) => handleFieldChange("link", e.target.value)}
							/>
							<p className="text-sm text-muted-foreground">
								Link para Discord, Telegram, WhatsApp ou outra plataforma da comunidade (opcional)
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
