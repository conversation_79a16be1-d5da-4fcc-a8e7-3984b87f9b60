import { useState, useCallback } from 'react'
import { toast } from 'sonner'

interface BunnyVideoUploadState {
  isUploading: boolean
  progress: number
  status: 'idle' | 'creating' | 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
  videoId?: string
  videoUrl?: string
}

interface UseBunnyVideoUploadOptions {
  onProgress?: (progress: number) => void
  onSuccess?: (videoUrl: string, videoId: string) => void
  onError?: (error: string) => void
}

const BUNNY_CONFIG = {
  apiKey: process.env.NEXT_PUBLIC_BUNNY_SIGNATURE || 'f390ed59-4afd-4d99-a4c1a7d6d35b-27ab-4c7b',
  libraryId: process.env.NEXT_PUBLIC_BUNNY_LIBRARY_ID || '453733',
  expire: process.env.NEXT_PUBLIC_BUNNY_EXPIRE || '1735689600',
  cdnHostname: process.env.NEXT_PUBLIC_BUNNY_CDN_HOSTNAME || 'vz-b5b41e2f-3e6.b-cdn.net'
}

export function useBunnyVideoUpload(options: UseBunnyVideoUploadOptions = {}) {
  const [uploadState, setUploadState] = useState<BunnyVideoUploadState>({
    isUploading: false,
    progress: 0,
    status: 'idle'
  })

  const isConfigured = !!(BUNNY_CONFIG.apiKey && BUNNY_CONFIG.libraryId)

  const createVideoObject = async (title: string): Promise<{ videoId: string; libraryId: string }> => {
    if (!BUNNY_CONFIG.apiKey || !BUNNY_CONFIG.libraryId) {
      throw new Error('Bunny.net não configurado')
    }

    const createResponse = await fetch(
      `https://video.bunnycdn.com/library/${BUNNY_CONFIG.libraryId}/videos`,
      {
        method: 'POST',
        headers: {
          'AccessKey': BUNNY_CONFIG.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          metadata: {
            source: 'web-upload',
            uploadedAt: new Date().toISOString()
          }
        }),
      }
    )

    if (!createResponse.ok) {
      const errorText = await createResponse.text()
      throw new Error(`Falha ao criar vídeo no Bunny.net: ${createResponse.status} - ${errorText}`)
    }

    const videoData = await createResponse.json()
    const videoId = videoData.guid

    return { videoId, libraryId: BUNNY_CONFIG.libraryId }
  }

  const uploadVideo = useCallback(async (file: File, title?: string) => {
    if (!isConfigured) {
      const error = 'Bunny.net não está configurado. Verifique as variáveis de ambiente.'
      toast.error(error)
      options.onError?.(error)
      return
    }

    if (!file.type.startsWith('video/')) {
      const error = 'Apenas arquivos de vídeo são aceitos'
      toast.error(error)
      options.onError?.(error)
      return
    }

    if (file.size > 5 * 1024 * 1024 * 1024) {
      const error = 'Arquivo muito grande. Tamanho máximo: 5GB'
      toast.error(error)
      options.onError?.(error)
      return
    }

    const videoTitle = title || file.name.replace(/\.[^/.]+$/, '')

    try {
      setUploadState({
        isUploading: true,
        progress: 0,
        status: 'creating'
      })

      toast.loading('🎬 Preparando upload do vídeo...', { id: 'video-upload' })

      const { videoId, libraryId } = await createVideoObject(videoTitle)

      setUploadState(prev => ({
        ...prev,
        status: 'uploading',
        videoId
      }))

      const uploadPromise = new Promise<string>((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100)

            setUploadState(prev => ({
              ...prev,
              progress
            }))

            options.onProgress?.(progress)

            if (progress === 50) {
              toast.loading('Upload em andamento...', { id: 'video-upload' })
            } else if (progress === 90) {
              toast.loading('Finalizando...', { id: 'video-upload' })
            }
          }
        })

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            setUploadState(prev => ({
              ...prev,
              status: 'processing',
              progress: 100
            }))

            toast.loading('Processando vídeo...', { id: 'video-upload' })

            setTimeout(() => {
              const videoUrl = `https://iframe.mediadelivery.net/embed/${libraryId}/${videoId}`

              setUploadState({
                isUploading: false,
                progress: 100,
                status: 'completed',
                videoId,
                videoUrl
              })

              toast.dismiss('video-upload')
              toast.success('✅ Vídeo enviado com sucesso!')
              options.onSuccess?.(videoUrl, videoId)
              resolve(videoUrl)
            }, 3000)
          } else {
            const errorMessage = `Upload falhou: ${xhr.status} - ${xhr.statusText}`

            setUploadState({
              isUploading: false,
              progress: 0,
              status: 'error',
              error: errorMessage
            })

            toast.dismiss('video-upload')
            toast.error(`❌ ${errorMessage}`)
            options.onError?.(errorMessage)
            reject(new Error(errorMessage))
          }
        })

        xhr.addEventListener('error', () => {
          const errorMessage = 'Erro de rede durante o upload'

          setUploadState({
            isUploading: false,
            progress: 0,
            status: 'error',
            error: errorMessage
          })

          toast.dismiss('video-upload')
          toast.error(`❌ ${errorMessage}`)
          options.onError?.(errorMessage)
          reject(new Error(errorMessage))
        })

        const uploadUrl = `https://video.bunnycdn.com/library/${libraryId}/videos/${videoId}`

        xhr.open('PUT', uploadUrl)
        xhr.setRequestHeader('AccessKey', BUNNY_CONFIG.apiKey!)
        xhr.setRequestHeader('Content-Type', file.type)
        xhr.send(file)
      })

      return uploadPromise

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro no upload do vídeo'

      setUploadState({
        isUploading: false,
        progress: 0,
        status: 'error',
        error: errorMessage
      })

      toast.dismiss('video-upload')
      toast.error(errorMessage)
      options.onError?.(errorMessage)
    }
  }, [isConfigured, options])

  const resetUpload = useCallback(() => {
    setUploadState({
      isUploading: false,
      progress: 0,
      status: 'idle'
    })
  }, [])

  return {
    uploadState,
    uploadVideo,
    resetUpload,
    isConfigured
  }
}
