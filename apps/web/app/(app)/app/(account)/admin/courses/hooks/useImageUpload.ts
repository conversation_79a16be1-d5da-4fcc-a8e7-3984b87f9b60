import { useState } from "react";
import { apiClient } from "@shared/lib/api-client";

interface UploadResponse {
	uploadUrl?: string;
	filePath: string;
	publicUrl: string;
}

interface UseImageUploadOptions {
	organizationId: string;
	fileType: "course-logo" | "module-cover" | "lesson-cover";
}

export function useImageUpload({ organizationId, fileType }: UseImageUploadOptions) {
	const [isUploading, setIsUploading] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);
	const [error, setError] = useState<string | null>(null);

	const uploadImage = async (file: File): Promise<string | null> => {
		// Validate inputs
		if (!file) {
			setError("Nenhum arquivo selecionado");
			return null;
		}

		if (!organizationId || organizationId.trim() === "") {
			setError("Selecione um workspace antes de fazer upload da imagem");
			return null;
		}

		// Validate file type
		if (!file.type.startsWith("image/")) {
			setError("Apenas arquivos de imagem são permitidos");
			return null;
		}

		// Validate file size (5MB limit)
		const maxSize = 5 * 1024 * 1024; // 5MB
		if (file.size > maxSize) {
			setError("O arquivo deve ter no máximo 5MB");
			return null;
		}

		setIsUploading(true);
		setError(null);
		setUploadProgress(0);

		try {
			const base64Data = await new Promise<string>((resolve, reject) => {
				const reader = new FileReader();
				reader.onload = () => resolve(reader.result as string);
				reader.onerror = reject;
				reader.readAsDataURL(file);
			});

			setUploadProgress(50);

			const uploadResponse = await apiClient.courses["upload-url"].$post({
				json: {
					fileName: file.name,
					fileType,
					organizationId,
					fileData: base64Data,
				},
			});

			if (!uploadResponse.ok) {
				const errorData = await uploadResponse.json() as { error?: string };
				throw new Error(errorData.error || "Falha ao fazer upload do arquivo");
			}

			const responseData = await uploadResponse.json();

			const { publicUrl }: UploadResponse = responseData;

			setUploadProgress(100);
			return publicUrl;
		} catch (err) {
			setError(err instanceof Error ? err.message : "Falha no upload");
			return null;
		} finally {
			setIsUploading(false);
		}
	};

	const resetUpload = () => {
		setIsUploading(false);
		setUploadProgress(0);
		setError(null);
	};

	return {
		uploadImage,
		isUploading,
		uploadProgress,
		error,
		resetUpload,
	};
}
