import { Card } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";

export function VitrineCardSkeleton() {
	return (
		<Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border hover:border-primary/30 overflow-hidden">
			<div className="p-6">
				{/* Header */}
				<div className="flex items-start justify-between mb-4">
					<div className="flex items-center gap-3">
						<Skeleton className="size-12 rounded-lg" />
						<div className="min-w-0 flex-1">
							<Skeleton className="h-6 w-3/4 mb-2" />
							<div className="flex items-center gap-2 mt-1">
								<Skeleton className="h-5 w-16 rounded-full" />
								<Skeleton className="h-3 w-24" />
							</div>
						</div>
					</div>
				</div>

				{/* Banner Image Section */}
				<div className="mb-4">
					<Skeleton className="w-full h-32 rounded-lg" />
				</div>

				{/* Description */}
				<div className="mb-4">
					<Skeleton className="h-4 w-full mb-2" />
					<Skeleton className="h-4 w-2/3" />
				</div>

				{/* Footer Buttons */}
				<div className="flex gap-2 mt-auto">
					<Skeleton className="flex-1 h-9 rounded-md" />
					<Skeleton className="flex-1 h-9 rounded-md" />
					<Skeleton className="h-9 w-9 rounded-md" />
				</div>
			</div>
		</Card>
	);
}

export function VitrineGridSkeleton({ count = 6 }: { count?: number }) {
	return (
		<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
			{Array.from({ length: count }).map((_, index) => (
				<VitrineCardSkeleton key={index} />
			))}
		</div>
	);
}
