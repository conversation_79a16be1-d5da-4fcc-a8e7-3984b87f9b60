import { CoursePage } from '@/modules/saas/organizations/course'

interface CoursePageProps {
  params: Promise<{
    organizationSlug: string
    courseId: string
  }>
  searchParams: Promise<{
    lesson?: string
  }>
}

export default async function CoursePageRoute({ params, searchParams }: CoursePageProps) {
  const { courseId, organizationSlug } = await params
  const { lesson: lessonId } = await searchParams
  return <CoursePage courseId={courseId} organizationSlug={organizationSlug} initialLessonId={lessonId} />
}