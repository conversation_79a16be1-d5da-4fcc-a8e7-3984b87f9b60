"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { hasRole, type UserRole } from "@/lib/auth/role-guard";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface RoleGuardProps {
	children: React.ReactNode;
	allowedRoles: UserRole[];
	redirectTo?: string;
	fallback?: React.ReactNode;
}

export function RoleGuard({ 
	children, 
	allowedRoles, 
	redirectTo = "/app", 
	fallback 
}: RoleGuardProps) {
	const { user } = useSession();
	const router = useRouter();

	useEffect(() => {
		if (user && !hasRole(user.role, allowedRoles)) {
			router.push(redirectTo);
		}
	}, [user, allowedRoles, redirectTo, router]);

	if (!user) {
		return (
			<div className="flex items-center justify-center min-h-[200px]">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
			</div>
		);
	}

	if (!hasRole(user.role, allowedRoles)) {
		if (fallback) {
			return <>{fallback}</>;
		}
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
					<p className="text-muted-foreground">Você não tem permissão para acessar esta área.</p>
				</div>
			</div>
		);
	}

	return <>{children}</>;
}

export function AdminGuard({ children, ...props }: Omit<RoleGuardProps, "allowedRoles">) {
	return (
		<RoleGuard allowedRoles={["admin"]} {...props}>
			{children}
		</RoleGuard>
	);
}

export function ProducerGuard({ children, ...props }: Omit<RoleGuardProps, "allowedRoles">) {
	return (
		<RoleGuard allowedRoles={["producer"]} {...props}>
			{children}
		</RoleGuard>
	);
}

export function AdminOrProducerGuard({ children, ...props }: Omit<RoleGuardProps, "allowedRoles">) {
	return (
		<RoleGuard allowedRoles={["admin", "producer"]} {...props}>
			{children}
		</RoleGuard>
	);
}