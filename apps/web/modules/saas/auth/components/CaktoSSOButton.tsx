"use client";

import { cn } from "@/lib/utils";
import { Logo } from "@/modules/shared/components/Logo";
import { config } from "@repo/config";
import { authClient } from "@repo/auth/client";
import { Button } from "@ui/components/button";
import { useQueryState } from "nuqs";
import { parseAsString } from "nuqs";

export function CaktoSSOButton({
	className,
	children,
	onClick,
}: {
	className?: string;
	children?: React.ReactNode;
	onClick?: () => void;
}) {
	const [invitationId] = useQueryState("invitationId", parseAsString);
	const [email] = useQueryState("email", parseAsString);

	const redirectPath = invitationId
		? `/app/organization-invitation/${invitationId}`
		: config.auth.redirectAfterSignIn;

	const onSignin = async () => {
		if (onClick) {
			onClick();
		}

		try {
			const callbackURL = new URL(redirectPath, window.location.origin);

			if (invitationId) {
				callbackURL.searchParams.set("invitationId", invitationId);
			}
			if (email) {
				callbackURL.searchParams.set("email", email);
			}

			console.log("[SSO DEBUG] Iniciando fluxo SSO", {
				providerId: "django-sso",
				callbackURL: callbackURL.toString(),
				redirectPath,
				invitationId,
				email,
				origin: window.location.origin
			});

			// Usar o Better Auth para iniciar o fluxo OAuth2
			const result = await authClient.signIn.oauth2({
				providerId: "django-sso",
				callbackURL: callbackURL.toString(),
			});

			console.log("[SSO DEBUG] Resultado do signIn.oauth2:", result);
		} catch (error) {
			console.error("[SSO ERROR] Erro ao iniciar SSO:", {
				error,
				message: error?.message,
				stack: error?.stack,
				code: error?.code,
				status: error?.status
			});
		}
	};

	return (
		<Button
			onClick={onSignin}
			variant="primary"
			type="button"
			className={cn(
				className,
				"bg-[#0F7864] hover:bg-[#0A5A4A] text-white",
			)}
		>
			<Logo color="white" withLabel={false} className="mr-2 size-5" />
			Entrar com a Cakto
		</Button>
	);
}
