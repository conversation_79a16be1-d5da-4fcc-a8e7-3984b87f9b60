import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { CoursePreviewData } from '../types/course-preview';

export function useCoursePreview(courseId: string, organizationSlug: string) {
  const [isEnabled, setIsEnabled] = useState(false);

  const {
    data: rawData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['course-preview', courseId, organizationSlug],
    queryFn: async () => {
      const response = await fetch(`/api/courses/${courseId}/preview?organizationSlug=${organizationSlug}`);

      if (!response.ok) {
        throw new Error('Failed to fetch course preview');
      }

      return response.json();
    },
    enabled: isEnabled && !!courseId && !!organizationSlug,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  // Transform the API response to match the CoursePreviewData interface
  const courseData: CoursePreviewData | undefined = rawData ? {
    id: rawData.id,
    title: rawData.title,
    description: rawData.description,
    logo: rawData.logo,
    image: rawData.logo, // Use logo as image fallback
    vitrineImage: rawData.logo, // Use logo as vitrineImage fallback
    backgroundImage: rawData.logo, // Use logo as backgroundImage fallback
    modules: rawData.modules || [],
    firstLesson: rawData.firstLesson
  } : undefined;

  const fetchCoursePreview = useCallback(() => {
    setIsEnabled(true);
    refetch();
  }, [refetch]);

  const reset = useCallback(() => {
    setIsEnabled(false);
  }, []);

  return {
    courseData,
    isLoading,
    error,
    fetchCoursePreview,
    reset
  };
}