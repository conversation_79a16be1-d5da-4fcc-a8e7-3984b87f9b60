"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@ui/components/button";
import { X, Play, Check, ChevronDown } from "lucide-react";
import { CoursePreviewModalProps, Lesson } from "../types/course-preview";

export const CoursePreviewModal = React.memo(function CoursePreviewModal({
  isOpen,
  onClose,
  course,
  onPlayLesson,
}: CoursePreviewModalProps) {
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);

  useEffect(() => {
    if (course.firstLesson) {
      setSelectedLesson(course.firstLesson);
    } else if (course.modules.length > 0 && course.modules[0].lessons.length > 0) {
      setSelectedLesson(course.modules[0].lessons[0]);
    }
  }, [course]);

  const handlePlayLesson = useCallback((lesson: Lesson) => {
    setSelectedLesson(lesson);
    onPlayLesson?.(lesson.id);
  }, [onPlayLesson]);

  const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);

  const backgroundImage = course.image || course.vitrineImage || course.backgroundImage || course.logo;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/90 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative w-full max-w-4xl bg-card rounded-lg overflow-hidden shadow-2xl border"
            onClick={(e) => e.stopPropagation()}
          >

            <div className="absolute top-4 right-4 z-20">
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground hover:bg-muted/30 rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>


            <div className="relative h-80 overflow-hidden">
              {backgroundImage ? (
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{ backgroundImage: `url(${backgroundImage})` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-background via-background/40 to-transparent" />
                </div>
              ) : (
                <div className="absolute inset-0 bg-gradient-to-br from-muted via-muted/80 to-background">
                  <div className="absolute inset-0 bg-gradient-to-t from-background via-background/40 to-transparent" />
                </div>
              )}


              <div className="absolute bottom-0 left-0 right-0 p-8">
                <p className="text-muted-foreground text-sm mb-2">Comece Aqui</p>
                <h1 className="text-3xl font-bold text-foreground mb-4">{course.title}</h1>


                {selectedLesson && (
                  <Button
                    onClick={() => handlePlayLesson(selectedLesson)}
                    className="bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-2.5 rounded-md font-semibold flex items-center gap-2"
                  >
                    <Play className="h-4 w-4 fill-current" />
                    Play
                  </Button>
                )}
              </div>
            </div>


            <div className="p-8">

              <div className="mb-6">
                <h2 className="text-2xl font-bold text-foreground mb-2">{course.title}</h2>
                <p className="text-muted-foreground text-sm">
                  {course.modules.length} módulo{course.modules.length > 1 ? 's' : ''} • {totalLessons} aula{totalLessons > 1 ? 's' : ''}
                </p>
              </div>


              {course.modules.length > 0 && (
                <div className="mb-6">
                  <div className="bg-muted/20 rounded-lg border border-border">
                    <div className="flex items-center justify-between p-4 cursor-pointer">
                      <span className="text-foreground font-medium">{course.modules[0].title}</span>
                      <ChevronDown className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </div>
                </div>
              )}

              {/* Lessons List */}
              {course.modules.length > 0 && course.modules[0].lessons.length > 0 && (
                <div className="space-y-3">
                  {course.modules[0].lessons.slice(0, 3).map((lesson) => (
                    <div
                      key={lesson.id}
                      className="bg-muted/20 rounded-lg p-4 border border-border cursor-pointer hover:bg-muted/30 transition-colors"
                      onClick={() => handlePlayLesson(lesson)}
                    >
                      <div className="flex items-center gap-4">

                        <div className="w-5 h-5 rounded border border-border flex items-center justify-center flex-shrink-0">
                          {lesson.isCompleted && (
                            <Check className="h-3 w-3 text-primary" />
                          )}
                        </div>


                        <div className="w-20 h-12 bg-muted rounded overflow-hidden flex-shrink-0">
                          {lesson.thumbnail ? (
                            <img
                              src={lesson.thumbnail}
                              alt={lesson.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <div className="text-primary font-bold text-xs">acquisition360</div>
                            </div>
                          )}
                        </div>


                        <div className="flex-1 min-w-0">
                          <h3 className="text-foreground font-medium text-base">
                            {lesson.title}
                          </h3>
                        </div>


                        <div className="text-muted-foreground text-sm flex-shrink-0">
                          {lesson.duration || "28 min"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
});
