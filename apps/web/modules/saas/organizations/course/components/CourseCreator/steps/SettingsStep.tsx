'use client'

import { useState, useCallback } from 'react'
import { CourseCreationData } from '../../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Textarea } from '@/modules/ui/components/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Badge } from '@/modules/ui/components/badge'
import {
  Settings,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

interface SettingsStepProps {
  data: CourseCreationData
  onUpdate: (data: Partial<CourseCreationData>) => void
  onNext: () => void
  onPrevious: () => void
  organizationSlug: string
}

export function SettingsStep({ data, onUpdate, onNext, onPrevious }: SettingsStepProps) {
  const [community, setCommunity] = useState(data.basicInfo.community || '')
  const [link, setLink] = useState(data.basicInfo.link || '')

  const handleNext = useCallback(() => {
    onUpdate({
      basicInfo: {
        ...data.basicInfo,
        community,
        link
      }
    })
    onNext()
  }, [onUpdate, onNext, data.basicInfo, community, link])

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-primary" />
            Configurações do Curso
          </CardTitle>
          <p className="text-muted-foreground">
            Configure as informações adicionais do seu curso
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="community">Comunidade</Label>
              <Input
                id="community"
                placeholder="Nome da comunidade relacionada"
                value={community}
                onChange={(e) => setCommunity(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                Nome da comunidade ou grupo relacionado ao curso (opcional)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="link">Link do Curso</Label>
              <Input
                id="link"
                placeholder="https://exemplo.com/curso"
                value={link}
                onChange={(e) => setLink(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                Link externo relacionado ao curso (opcional)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious}>
          Anterior
        </Button>
        <Button onClick={handleNext}>
          Continuar
        </Button>
      </div>
    </div>
  )
}
