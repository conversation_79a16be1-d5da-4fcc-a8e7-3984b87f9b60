export type UserRole = "user" | "admin" | "producer";

export function hasRole(userRole: string | null | undefined, allowedRoles: UserRole[]): boolean {
	if (!userRole) return false;
	return allowedRoles.includes(userRole as UserRole);
}

export function isAdmin(userRole: string | null | undefined): boolean {
	return hasRole(userRole, ["admin"]);
}

export function isProducer(userRole: string | null | undefined): boolean {
	return hasRole(userRole, ["producer"]);
}

export function isUser(userRole: string | null | undefined): boolean {
	return hasRole(userRole, ["user"]) || userRole === null || userRole === undefined;
}
