import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createProductAssociation() {
  try {
    console.log('🔍 Verificando cursos disponíveis...');
    
    const courses = await prisma.courses.findMany({
      select: {
        id: true,
        name: true
      }
    });
    
    if (courses.length === 0) {
      console.log('❌ Nenhum curso encontrado.');
      return;
    }
    
    console.log(`📚 Cursos encontrados: ${courses.length}`);
    courses.forEach(course => {
      console.log(`  - ${course.name} (${course.id})`);
    });
    
    const productId = 'a6266a5d-bfae-4561-a181-fc8fc17a3981';
    const firstCourse = courses[0];
    
    console.log(`\n🔗 Criando associação entre produto ${productId} e curso ${firstCourse.name}...`);
    
    const existingAssociation = await prisma.courseProduct.findFirst({
      where: {
        caktoProductId: productId
      }
    });
    
    if (existingAssociation) {
      console.log('✅ Associação já existe!');
      return;
    }
    
    const association = await prisma.courseProduct.create({
      data: {
        caktoProductId: productId,
        courseId: firstCourse.id,
        caktoProductName: 'Como vender na Cakto'
      }
    });
    
    console.log('✅ Associação criada com sucesso!');
    console.log(`   ID da associação: ${association.id}`);
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createProductAssociation();