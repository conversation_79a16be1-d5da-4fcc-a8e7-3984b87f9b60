import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const prisma = new PrismaClient();

async function fixUserSession() {
  try {
    console.log('🔍 Verificando e corrigindo sessão do usuário...');
    
    const user = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    
    if (!user) {
      console.log('❌ Usuário não encontrado');
      return;
    }
    
    console.log(`👤 Usuário: ${user.email}`);
    
    const existingSessions = await prisma.session.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    console.log(`📋 Sessões existentes: ${existingSessions.length}`);
    
    const now = new Date();
    const validSessions = existingSessions.filter(session => 
      session.expiresAt && new Date(session.expiresAt) > now
    );
    
    console.log(`✅ Sessões válidas: ${validSessions.length}`);
    
    if (validSessions.length === 0) {
      console.log('🔄 Criando nova sessão válida...');
      
      await prisma.session.deleteMany({
        where: {
          userId: user.id
        }
      });
      
      const newSession = await prisma.session.create({
        data: {
          userId: user.id,
          token: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          ipAddress: '127.0.0.1',
          userAgent: 'Test Session'
        }
      });
      
      console.log(`✅ Nova sessão criada: ${newSession.token}`);
      console.log(`   Expira em: ${newSession.expiresAt}`);
    } else {
      const latestSession = validSessions[0];
      console.log(`✅ Usando sessão válida existente: ${latestSession.token}`);
      console.log(`   Expira em: ${latestSession.expiresAt}`);
    }
    
    const finalSessions = await prisma.session.findMany({
      where: {
        userId: user.id,
        expiresAt: {
          gt: new Date()
        }
      }
    });
    
    console.log(`\n📊 Status final:`);
    console.log(`   Sessões válidas: ${finalSessions.length}`);
    
    if (finalSessions.length > 0) {
      console.log(`   Token da sessão ativa: ${finalSessions[0].token}`);
      console.log('\n🎯 Para testar no browser, use este cookie:');
      console.log(`   better-auth.session_token=${finalSessions[0].token}`);
    }
    
  } catch (error) {
    console.error('❌ Erro ao corrigir sessão:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixUserSession();