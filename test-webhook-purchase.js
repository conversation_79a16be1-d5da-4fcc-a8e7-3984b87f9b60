import "dotenv/config";

const WEBHOOK_URL = "http://localhost:3000/api/webhooks/cakto/purchase";
const USER_ID = "cmdqkl4610002l504t2qxiqes";

const webhookPayload = {
	secret: "1340098d-340d-488a-af83-f80e0eaaa773",
	event: "purchase_approved",
	data: {
		id: "9b39b030-a7f3-41e9-8558-902ca2340e29",
		refId: "BS3afYS",
		customer: {
			name: "<PERSON>",
			birthDate: null,
			email: "<EMAIL>",
			phone: "5554997039892",
			docNumber: "00763109223",
		},

		offer: {
			id: "7rohg3i",
			name: "Como vender na Cakto",
			price: 5.0,
		},
		offer_type: "main",
		product: {
			name: "Como vender na Cakto",
			id: "a6266a5d-bfae-4561-a181-fc8fc17a3981",
			short_id: "4fTU7AD",
			supportEmail: "<EMAIL>",
			type: "unique",
			invoiceDescription: "",
		},
		checkout: 31133,
		parent_order: "",
		subscription: null,
		subscription_period: null,
		checkoutUrl: "https://pay.cakto.com.br/7rohg3i",
		status: "paid",
		baseAmount: 5.0,
		discount: 0.00,
		amount: 5.0,
		commissions: [
			{
				user: "<EMAIL>",
				totalAmount: 2.51,
				percentage: 100.0,
				type: "producer",
			},
		],
		fees: 2.49,
		couponCode: null,
		reason: "Pagamento recusado, por favor tente novamente,",
		refund_reason: null,
		paymentMethod: "pix",
		paymentMethodName: "PIX",
		installments: 1,
		utm_source: null,
		utm_medium: null,
		utm_campaign: null,
		utm_term: null,
		utm_content: null,
		sck: null,
		fbc: null,
		fbp: "fb.2.1752787988663.989579505668916235",
		createdAt: "2025-08-07T15:29:31.893309-03:00",
		due_date: null,
		paidAt: "2025-08-07T15:30:10.486785-03:00",
		refundedAt: null,
		chargedbackAt: null,
		canceledAt: null,
		pix: {
			qrCode: "00020101021226830014BR.GOV.BCB.PIX2561qrcodespix.sejaefi.com.br/v2/75615a21b3774bdfb18ed24b5c34831f5204000053039865802BR5905EFISA6008SAOPAULO62070503***63047B9A",
			expirationDate: "2025-08-08 18:29:33.201063+00:00",
		},
	},
};

async function testWebhook() {
	try {
		console.log("🚀 Testando webhook de compra aprovada...");
		console.log(
			`📧 Email do cliente: ${webhookPayload.data.customer.email}`
		);
		console.log(`🛍️ Produto: ${webhookPayload.data.product.name}`);
		console.log(`💰 Valor: R$ ${webhookPayload.data.amount}`);
		console.log(`🔗 Produto ID: ${webhookPayload.data.product.id}`);
		console.log(`👤 Usuário de teste: ${USER_ID}`);
		console.log("");

		const response = await fetch(WEBHOOK_URL, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"User-Agent": "Cakto-Webhook/1.0",
			},
			body: JSON.stringify(webhookPayload),
		});

		console.log(`📊 Status da resposta: ${response.status}`);

		if (response.ok) {
			const result = await response.text();
			console.log("✅ Webhook processado com sucesso!");
			console.log("📄 Resposta:", result || "Sem conteúdo");
		} else {
			const error = await response.text();
			console.log("❌ Erro no webhook:");
			console.log("📄 Resposta de erro:", error);
		}
	} catch (error) {
		console.error("💥 Erro ao enviar webhook:", error.message);
	}
}

testWebhook();
