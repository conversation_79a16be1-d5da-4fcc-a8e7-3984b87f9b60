import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config({ path: '.env.local' });

const prisma = new PrismaClient();

async function debugAuthIssue() {
  try {
    console.log('🔍 Debugging authentication issue...');
    
    const user = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    
    if (!user) {
      console.log('❌ No user found');
      return;
    }
    
    console.log(`👤 User: ${user.email} (${user.id})`);
    
    const session = await prisma.session.findFirst({
      where: {
        userId: user.id,
        expiresAt: {
          gt: new Date()
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!session) {
      console.log('❌ No valid session found');
      return;
    }
    
    console.log(`✅ Session found: ${session.token}`);
    console.log(`   Expires: ${session.expiresAt}`);
    console.log(`   User ID: ${session.userId}`);
    
    console.log('\n🔍 Testing different cookie formats...');
    
    const cookieFormats = [
      `better-auth.session_token=${session.token}`,
      `session_token=${session.token}`,
      `authjs.session-token=${session.token}`,
      `next-auth.session-token=${session.token}`
    ];
    
    for (const cookieFormat of cookieFormats) {
      console.log(`\n🧪 Testing with cookie: ${cookieFormat}`);
      
      try {
        const response = await fetch('http://localhost:3000/api/vitrines/organization/vitrine?organizationSlug=cakto-members', {
          headers: {
            'Cookie': cookieFormat,
            'Content-Type': 'application/json',
            'User-Agent': 'Debug Script'
          }
        });
        
        console.log(`   Status: ${response.status}`);
        
        if (response.status === 200) {
          const data = await response.json();
          console.log('   ✅ Success! Data received');
          break;
        } else {
          const errorText = await response.text();
          console.log(`   ❌ Error: ${errorText}`);
        }
      } catch (error) {
        console.log(`   ❌ Request failed: ${error.message}`);
      }
    }
    
    console.log('\n🔍 Testing auth endpoint directly...');
    
    try {
      const authResponse = await fetch('http://localhost:3000/api/auth/session', {
        headers: {
          'Cookie': `better-auth.session_token=${session.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`Auth endpoint status: ${authResponse.status}`);
      
      if (authResponse.status === 200) {
        const authData = await authResponse.json();
        console.log('Auth data:', JSON.stringify(authData, null, 2));
      } else {
        const authError = await authResponse.text();
        console.log(`Auth error: ${authError}`);
      }
    } catch (error) {
      console.log(`Auth request failed: ${error.message}`);
    }
    
    console.log('\n🔍 Checking session in database...');
    
    const dbSession = await prisma.session.findUnique({
      where: {
        token: session.token
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true
          }
        }
      }
    });
    
    if (dbSession) {
      console.log('✅ Session exists in database:');
      console.log(`   Token: ${dbSession.token}`);
      console.log(`   User: ${dbSession.user.email}`);
      console.log(`   Role: ${dbSession.user.role}`);
      console.log(`   Expires: ${dbSession.expiresAt}`);
      console.log(`   Is expired: ${new Date() > dbSession.expiresAt}`);
    } else {
      console.log('❌ Session not found in database');
    }
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugAuthIssue();