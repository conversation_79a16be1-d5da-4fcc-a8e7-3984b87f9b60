import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config({ path: '.env.local' });

const prisma = new PrismaClient();

async function testCookieNames() {
  try {
    console.log('🔍 Testando diferentes nomes de cookie do Better Auth...');
    
    const user = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    
    if (!user) {
      console.log('❌ Usuário não encontrado');
      return;
    }
    
    console.log(`👤 Usuário: ${user.email}`);
    
    const session = await prisma.session.findFirst({
      where: {
        userId: user.id,
        expiresAt: {
          gt: new Date()
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!session) {
      console.log('❌ Nenhuma sessão válida encontrada');
      return;
    }
    
    console.log(`✅ Sessão encontrada: ${session.token}`);
    
    // Lista de possíveis nomes de cookie do Better Auth
    const cookieNames = [
      'better-auth.session_token',
      'better-auth.session-token', 
      'better_auth.session_token',
      'better_auth.session-token',
      'betterauth.session_token',
      'betterauth.session-token',
      'session_token',
      'session-token',
      'sessionToken',
      'auth.session_token',
      'auth.session-token',
      'authjs.session-token',
      'next-auth.session-token'
    ];
    
    console.log('\n🔍 Testando diferentes nomes de cookie...');
    
    for (const cookieName of cookieNames) {
      console.log(`\n🧪 Testando: ${cookieName}=${session.token}`);
      
      try {
        const response = await fetch('http://localhost:3000/api/auth/get-session', {
          headers: {
            'Cookie': `${cookieName}=${session.token}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`   Status: ${response.status}`);
        
        if (response.status === 200) {
          const data = await response.json();
          if (data && data.user) {
            console.log('   ✅ SUCESSO! Cookie correto encontrado!');
            console.log(`   Nome do cookie: ${cookieName}`);
            console.log(`   User: ${data.user.email}`);
            
            // Testar na API de vitrines
            console.log('\n🔍 Testando na API de vitrines...');
            const vitrineResponse = await fetch('http://localhost:3000/api/vitrines/organization/vitrine?organizationSlug=cakto-members', {
              headers: {
                'Cookie': `${cookieName}=${session.token}`,
                'Content-Type': 'application/json'
              }
            });
            
            console.log(`   Status vitrine: ${vitrineResponse.status}`);
            
            if (vitrineResponse.status === 200) {
              console.log('   ✅ API de vitrines funcionando!');
            } else {
              const errorText = await vitrineResponse.text();
              console.log(`   ❌ Erro na API de vitrines: ${errorText}`);
            }
            
            return; // Parar quando encontrar o cookie correto
          } else {
            console.log('   ⚠️  Status 200 mas sem dados de usuário');
          }
        } else {
          console.log('   ❌ Falhou');
        }
      } catch (error) {
        console.log(`   ❌ Erro: ${error.message}`);
      }
    }
    
    console.log('\n❌ Nenhum nome de cookie funcionou');
    
    // Verificar se existe alguma configuração específica
    console.log('\n🔍 Verificando configurações no banco...');
    
    const allSessions = await prisma.session.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: {
          select: {
            email: true
          }
        }
      }
    });
    
    console.log('\n📋 Últimas 5 sessões no banco:');
    allSessions.forEach((s, i) => {
      console.log(`   ${i + 1}. Token: ${s.token}`);
      console.log(`      User: ${s.user.email}`);
      console.log(`      Expires: ${s.expiresAt}`);
      console.log(`      Valid: ${new Date() < new Date(s.expiresAt)}`);
    });
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCookieNames();