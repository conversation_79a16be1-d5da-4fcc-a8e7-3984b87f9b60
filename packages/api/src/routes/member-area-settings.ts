import { db } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../middleware/auth";
import { getSignedUploadUrl, uploadFile, getSignedUrl } from "@repo/storage";

const memberAreaSettingsSchema = z.object({
	memberAreaName: z.string().optional(),
	primaryColor: z.string().optional(),
	logoUrl: z.string().optional(),
	commentsEnabled: z.boolean().optional(),
	supportEmail: z.string().optional(),
	menuItems: z.any().default([]),
	footer: z.any().default({}),
	subdomain: z.string().optional(),
	customDomain: z.string().optional(),
});

const uploadUrlSchema = z.object({
	fileName: z.string(),
	fileType: z.string(),
	organizationId: z.string(),
	fileData: z.string().optional(),
});



export const memberAreaSettingsRouter = new Hono()
	.basePath("/member-area-settings")
	.use(authMiddleware)
	.get(
		"/",
		describeRoute({
			summary: "Get member area settings for user's organization",
			tags: ["Member Area Settings"],
		}),
		async (c) => {
			const user = c.get("user");

			// Admins can access settings for any organization, regular users need membership
			let organizationId: string;

			if (user.role === 'admin') {
				// For admins, we could get organizationId from query params or return all settings
				// For now, let's get the first organization or handle this differently
				const firstOrg = await db.organization.findFirst();
				if (!firstOrg) {
					return c.json({ error: "No organization found" }, 404);
				}
				organizationId = firstOrg.id;
			} else {
				const membership = await db.member.findFirst({
					where: { userId: user.id },
					include: { organization: true },
				});

				if (!membership) {
					return c.json({ error: "No organization found" }, 404);
				}
				organizationId = membership.organizationId;
			}

			const settings = await db.memberAreaSettings.findFirst({
				where: { organizationId },
			});

			return c.json(settings);
		}
	)
	.post(
		"/",
		validator("json", memberAreaSettingsSchema),
		describeRoute({
			summary: "Create member area settings for user's organization",
			tags: ["Member Area Settings"],
		}),
		async (c) => {
			const user = c.get("user");
			const data = c.req.valid("json");

			// Admins can create settings for any organization, regular users need membership
			let organizationId: string;

			if (user.role === 'admin') {
				// For admins, organizationId should come from the request data
				if (!data.organizationId) {
					return c.json({ error: "Organization ID is required for admin users" }, 400);
				}
				organizationId = data.organizationId;
			} else {
				const membership = await db.member.findFirst({
					where: { userId: user.id },
					include: { organization: true },
				});

				if (!membership) {
					return c.json({ error: "No organization found" }, 404);
				}
				organizationId = membership.organizationId;
			}

			const existingSettings = await db.memberAreaSettings.findFirst({
				where: { organizationId },
			});

			if (existingSettings) {
				return c.json({ error: "Settings already exist for this organization" }, 409);
			}

			const settings = await db.memberAreaSettings.create({
				data: {
					...data,
					organizationId,
					menuItems: data.menuItems || [],
					footer: data.footer || {},
				},
			});

			return c.json(settings, 201);
		}
	)
	.patch(
		"/",
		validator("json", memberAreaSettingsSchema.partial()),
		describeRoute({
			summary: "Update member area settings for user's organization",
			tags: ["Member Area Settings"],
		}),
		async (c) => {
			const user = c.get("user");
			const data = c.req.valid("json");

			// Admins can update settings for any organization, regular users need membership
			let organizationId: string;

			if (user.role === 'admin') {
				// For admins, organizationId should come from the request data
				if (!data.organizationId) {
					return c.json({ error: "Organization ID is required for admin users" }, 400);
				}
				organizationId = data.organizationId;
			} else {
				const membership = await db.member.findFirst({
					where: { userId: user.id },
					include: { organization: true },
				});

				if (!membership) {
					return c.json({ error: "No organization found" }, 404);
				}
				organizationId = membership.organizationId;
			}

			const existingSettings = await db.memberAreaSettings.findFirst({
				where: { organizationId },
			});

			let settings;
			if (existingSettings) {
				settings = await db.memberAreaSettings.update({
					where: { id: existingSettings.id },
					data: data,
				});
			} else {
				settings = await db.memberAreaSettings.create({
					data: {
						...data,
						organizationId,
						menuItems: data.menuItems || [],
						footer: data.footer || {},
					},
				});
			}

			return c.json(settings);
		}
	)
	.post(
		"/upload-url",
		validator("json", uploadUrlSchema),
		describeRoute({
			summary: "Generate upload URL for member area settings images",
			tags: ["Member Area Settings"],
		}),
		async (c) => {
			try {
				const user = c.get("user");
				const { fileName, fileType, organizationId, fileData } = c.req.valid("json");

				if (!fileName || !fileType || !organizationId) {
					return c.json({ error: "Required data not provided" }, 400);
				}

				const membership = await db.member.findFirst({
					where: {
						userId: user.id,
						organizationId,
					},
				});

				if (!membership) {
					return c.json({ error: "User is not a member of this organization" }, 403);
				}

				if (membership.role === "member") {
					return c.json({ error: "Only administrators can upload images" }, 403);
				}

				const fileExtension = fileName.split('.').pop();
				const timestamp = Date.now();
				const uniqueFileName = `${timestamp}_${Math.random().toString(36).substring(7)}.${fileExtension}`;
				const filePath = `member-area-settings/${organizationId}/${fileType}/${uniqueFileName}`;

				const bucket = process.env.S3_BUCKET_NAME;
				if (!bucket) {
					return c.json({ error: "Storage configuration missing" }, 500);
				}

				const getContentType = (fileName: string) => {
					const ext = fileName.split('.').pop()?.toLowerCase();
					switch (ext) {
						case 'jpg':
						case 'jpeg':
							return 'image/jpeg';
						case 'png':
							return 'image/png';
						case 'gif':
							return 'image/gif';
						case 'webp':
							return 'image/webp';
						case 'svg':
							return 'image/svg+xml';
						default:
							return 'image/jpeg';
					}
				};

				const contentType = getContentType(fileName);

				if (fileData) {
					try {
						const buffer = Buffer.from(fileData.split(',')[1], 'base64');

						await uploadFile(filePath, buffer, {
							bucket,
							contentType,
						});

						const accessUrl = await getSignedUrl(filePath, {
							bucket,
							expiresIn: 3600 * 24 * 7,
						});

						return c.json({
							filePath,
							publicUrl: accessUrl,
						});
					} catch (uploadError) {
						console.error("Error uploading file:", uploadError);
						return c.json({ error: "Error uploading file" }, 500);
					}
				}

				const uploadUrl = await getSignedUploadUrl(filePath, {
					bucket,
					contentType
				});

				return c.json({
					uploadUrl,
					filePath,
					publicUrl: `${process.env.CDN_BASE_URL || process.env.S3_ENDPOINT}/${bucket}/${filePath}`,
				});
			} catch (error) {
				console.error("Error generating upload URL:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	);
