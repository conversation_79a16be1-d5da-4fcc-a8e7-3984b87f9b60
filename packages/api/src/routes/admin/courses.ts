import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";
import { db } from "@repo/database";

const querySchema = z.object({
	organizationSlug: z.string().optional(),
	limit: z.coerce.number().min(1).max(100).default(50),
	offset: z.coerce.number().min(0).default(0),
});

const paramsSchema = z.object({
	courseId: z.string(),
});

export const coursesRouter = new Hono()
	.basePath("/courses")
	.use(authMiddleware)
	.use(adminMiddleware)
	.get(
		"/",
		validator("query", querySchema),
		describeRoute({
			summary: "Get admin courses",
			tags: ["Courses"],
		}),
		async (c) => {
			try {
				const { organizationSlug, limit, offset } =
					c.req.valid("query");

				// Build where conditions
				const where: any = {};

				// Filter by organization if specified
				if (organizationSlug) {
					const organization = await db.organization.findFirst({
						where: { slug: organizationSlug },
						select: { id: true },
					});

					if (!organization) {
						return c.json({ error: "Organization not found" }, 404);
					}

					where.organizationId = organization.id;
				}

				// Get courses with comprehensive data
				const courses = await db.courses.findMany({
					where,
					include: {
						organization: {
							select: {
								id: true,
								name: true,
								slug: true,
							},
						},
						creator: {
							select: {
								id: true,
								name: true,
								email: true,
							},
						},
						courseModules: {
							include: {
								module: {
									select: {
										id: true,
										name: true,
										position: true,
									},
								},
							},
							orderBy: { module: { position: "asc" } },
						},
						courseProduct: {
							select: {
								id: true,
								caktoProductId: true,
								caktoProductName: true,
								createdAt: true,
								updatedAt: true,
							},
						},
						_count: {
							select: {
								courseModules: true,
							},
						},
					},
					orderBy: { createdAt: "desc" },
					take: limit,
					skip: offset,
				});

				// Get total count for pagination
				const totalCount = await db.courses.count({ where });

				// Get organizations with course counts
				const organizations = await db.organization.findMany({
					select: {
						id: true,
						name: true,
						slug: true,
					},
					orderBy: { name: "asc" },
				});

				// Get course counts for each organization
				const orgCounts = await Promise.all(
					organizations.map(async (org) => {
						const count = await db.courses.count({
							where: { organizationId: org.id },
						});
						return { ...org, coursesCount: count };
					})
				);

				// Format response
				const formattedCourses = courses.map((course: any) => ({
					id: course.id,
					name: course.name,
					description: course.description,
					logo: course.logo,
					community: course.community,
					link: course.link,
					studentsCount: 0,
					modulesCount: course._count.courseModules,
					moduleCount: course._count.courseModules,
					status: "published",
					type: "course",
					createdAt: course.createdAt.toISOString(),
					updatedAt: course.updatedAt.toISOString(),
					organizationId: course.organizationId,
					organization: course.organization,
					creator: course.creator,
					modules: course.courseModules.map((cm: any) => ({
						id: cm.module.id,
						name: cm.module.name,
						position: cm.module.position,
					})),
					caktoProducts: course.courseProduct.map((cp: any) => ({
						id: cp.id,
						caktoProductId: cp.caktoProductId,
						caktoProductName: cp.caktoProductName,
						createdAt: cp.createdAt.toISOString(),
						updatedAt: cp.updatedAt.toISOString(),
					})),
				}));

				return c.json({
					courses: formattedCourses,
					totalCount,
					organizations: orgCounts,
					pagination: {
						total: totalCount,
						limit,
						offset,
						hasMore: offset + limit < totalCount,
					},
				});
			} catch (error) {
				console.error("Error fetching admin courses:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	)
	.get(
		"/:courseId",
		validator("param", paramsSchema),
		describeRoute({
			summary: "Get admin course details",
			tags: ["Courses"],
		}),
		async (c) => {
			try {
				const { courseId } = c.req.valid("param");

				const course = await db.courses.findFirst({
					where: { id: courseId },
					include: {
						organization: {
							select: {
								id: true,
								name: true,
								slug: true,
							},
						},
						creator: {
							select: {
								id: true,
								name: true,
								email: true,
							},
						},
						courseModules: {
							include: {
								module: {
									include: {
										lessons: {
											orderBy: { position: "asc" },
										},
									},
								},
							},
							orderBy: { module: { position: "asc" } },
						},
					},
				});

				if (!course) {
					return c.json({ error: "Course not found" }, 404);
				}

				const formattedCourse = {
					id: course.id,
					name: course.name,
					description: course.description,
					logo: course.logo,
					community: course.community,
					link: course.link,
					createdAt: course.createdAt.toISOString(),
					updatedAt: course.updatedAt.toISOString(),
					organization: course.organization,
					creator: course.creator,
					modules: course.courseModules.map((cm: any) => ({
						id: cm.module.id,
						name: cm.module.name,
						position: cm.module.position,
						cover: cm.module.cover,
						lessons: cm.module.lessons.map((lesson: any) => ({
							id: lesson.id,
							name: lesson.name,
							description: lesson.description,
							videoUrl: lesson.videoUrl,
							position: lesson.position,
							duration: lesson.duration,
						})),
					})),
				};

				return c.json(formattedCourse);
			} catch (error) {
				console.error("Error fetching admin course:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	)
	.delete(
		"/:courseId",
		validator("param", paramsSchema),
		describeRoute({
			summary: "Delete admin course",
			tags: ["Courses"],
		}),
		async (c) => {
			try {
				const { courseId } = c.req.valid("param");

				// Check if course exists
				const course = await db.courses.findFirst({
					where: { id: courseId },
					select: { id: true, name: true },
				});

				if (!course) {
					return c.json({ error: "Course not found" }, 404);
				}

				// Delete the course (this will cascade delete related records)
				await db.courses.delete({
					where: { id: courseId },
				});

				return c.json({
					message: "Course deleted successfully",
					deletedCourse: {
						id: course.id,
						name: course.name,
					},
				});
			} catch (error) {
				console.error("Error deleting admin course:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	);
