import { countAllUsers, getUsers, createUser, createUserAccount, getUserByEmail, db } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";
import { auth } from "@repo/auth";
import { sendEmail } from "@repo/mail";
import { getBaseUrl } from "@repo/utils";
import { logger } from "@repo/logs";

const createUserSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	role: z.enum(["user", "admin", "producer"]).default("user"),
	sendEmail: z.boolean().default(true),
	mainAppUserId: z.string().optional(),
});

const createProducerSchema = z.object({
	name: z.string().min(1, "Nome é obrigatório"),
	email: z.string().email("Email inválido"),
	mainAppUserId: z.string().optional(),
	sendEmail: z.boolean().default(true),
});

export const userRouter = new Hono()
	.basePath("/users")
	.use(adminMiddleware)
	.get(
		"/",
		validator(
			"query",
			z.object({
				query: z.string().optional(),
				limit: z.string().optional().default("10").transform(Number),
				offset: z.string().optional().default("0").transform(Number),
			}),
		),
		describeRoute({
			summary: "Get all users",
			tags: ["Administration"],
		}),
		async (c) => {
			const { query, limit, offset } = c.req.valid("query");

			const users = await getUsers({
				limit,
				offset,
				query,
			});

			const total = await countAllUsers();

			return c.json({ users, total });
		},
	)
	.post(
		"/",
		validator("json", createUserSchema),
		describeRoute({
			summary: "Create a new user",
			tags: ["Administration"],
		}),
		async (c) => {
			try {
				const { name, email, role, sendEmail: shouldSendEmail } = c.req.valid("json");

				// Check if user already exists
				const existingUser = await getUserByEmail(email);
				if (existingUser) {
					return c.json({ error: "Usuário com este email já existe" }, 400);
				}

				// Generate random password
				const authContext = await auth.$context;
				const password = Math.random().toString(36).slice(-12) + Math.random().toString(36).toUpperCase().slice(-4);
				const hashedPassword = await authContext.password.hash(password);

				// Create user using database functions (Better Auth pattern)
				const user = await createUser({
					email,
					name,
					role,
					emailVerified: true,
					onboardingComplete: false,
				});

				if (!user) {
					return c.json({ error: "Falha ao criar usuário" }, 500);
				}

				// Create user account with password
				await createUserAccount({
					userId: user.id,
					providerId: "credential",
					accountId: user.id,
					hashedPassword,
				});

				// Send welcome email if requested
				if (shouldSendEmail) {
					const loginUrl = `${getBaseUrl()}/auth/login`;

					await sendEmail({
						to: email,
						templateId: "userCreated",
						context: {
							name,
							email,
							password,
							url: loginUrl,
						},
						locale: "pt",
					});
				}

				return c.json({
					success: true,
					user: {
						id: user.id,
						email: user.email,
						name: user.name,
						role: user.role,
					},
					credentials: shouldSendEmail ? { email, password } : null,
				});

			} catch (error) {
				console.error("Error creating user:", error);
				return c.json({ error: "Erro interno do servidor" }, 500);
			}
		},
	)
	.post(
		"/producer",
		validator("json", createProducerSchema),
		describeRoute({
			summary: "Create a new producer",
			tags: ["Administration"],
		}),
		async (c) => {
			try {
				const { name, email, mainAppUserId, sendEmail: shouldSendEmail } = c.req.valid("json");

				logger.info(`Creating producer user`, {
					email,
					name,
					mainAppUserId,
					sendEmail: shouldSendEmail
				});

				// Check if user already exists
				const existingUser = await getUserByEmail(email);
				if (existingUser) {
					logger.warn(`Producer creation failed - user already exists`, {
						email,
						existingUserId: existingUser.id,
						existingUserRole: existingUser.role
					});
					return c.json({
						success: false,
						error: "USER_ALREADY_EXISTS",
						message: "Usuário com este email já existe"
					}, 400);
				}

				// Create producer user (without password - will use magic link)
				const user = await createUser({
					email,
					name,
					role: "producer",
					emailVerified: true,
					onboardingComplete: false,
					mainAppUserId: mainAppUserId || null,
				});

				if (!user) {
					logger.error(`Failed to create producer user`, { email, name });
					return c.json({
						success: false,
						error: "USER_CREATION_FAILED",
						message: "Falha ao criar produtor"
					}, 500);
				}

				logger.info(`Producer user created successfully`, {
					userId: user.id,
					email: user.email,
					name: user.name,
					mainAppUserId: user.mainAppUserId
				});

				// Send welcome email if requested
				if (shouldSendEmail) {
					try {
						await sendEmail({
							to: email,
							templateId: "producerWelcome",
							context: {
								name,
								email,
							},
							locale: "pt",
						});
						logger.info(`Producer welcome email sent successfully`, {
							userId: user.id,
							email: user.email
						});
					} catch (emailError) {
						logger.error(`Failed to send producer welcome email`, {
							userId: user.id,
							email: user.email,
							error: emailError
						});
					}
				}

				return c.json({
					success: true,
					user: {
						id: user.id,
						email: user.email,
						name: user.name,
						role: user.role,
						mainAppUserId: user.mainAppUserId,
					},
				});

			} catch (error) {
				logger.error(`Error creating producer`, {
					error: error instanceof Error ? error.message : "Unknown error",
					stack: error instanceof Error ? error.stack : undefined
				});
				return c.json({
					success: false,
					error: "INTERNAL_SERVER_ERROR",
					message: "Erro interno do servidor"
				}, 500);
			}
		},
	)
	.get(
		"/main-app/:mainAppUserId",
		describeRoute({
			summary: "Get user by main app user ID",
			tags: ["Administration"],
		}),
		async (c) => {
			try {
				const mainAppUserId = c.req.param("mainAppUserId");

				logger.info(`Looking up user by main app ID`, { mainAppUserId });

				const user = await db.user.findUnique({
					where: { mainAppUserId },
				});

				if (!user) {
					logger.warn(`User not found by main app ID`, { mainAppUserId });
					return c.json({
						success: false,
						error: "USER_NOT_FOUND",
						message: "Usuário não encontrado"
					}, 404);
				}

				logger.info(`User found by main app ID`, {
					mainAppUserId,
					userId: user.id,
					email: user.email,
					role: user.role
				});

				return c.json({
					success: true,
					data: {
						id: user.id,
						email: user.email,
						name: user.name,
						role: user.role,
						mainAppUserId: user.mainAppUserId,
					},
				});

			} catch (error) {
				logger.error(`Error getting user by main app ID`, {
					mainAppUserId: c.req.param("mainAppUserId"),
					error: error instanceof Error ? error.message : "Unknown error",
					stack: error instanceof Error ? error.stack : undefined
				});
				return c.json({
					success: false,
					error: "INTERNAL_SERVER_ERROR",
					message: "Erro interno do servidor"
				}, 500);
			}
		},
	);
