import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { getSignedUploadUrl, uploadFile, getSignedUrl } from "@repo/storage";
import { db } from "@repo/database";
import type { Session } from "@repo/auth";

const uploadRequestSchema = z.object({
	fileName: z.string().min(1, "File name is required"),
	fileType: z.enum(["course-logo", "module-cover", "lesson-cover"]),
	organizationId: z.string(),
	fileData: z.string().optional(), // Base64 encoded file data
});

export const uploadImage = new Hono()
	.use(authMiddleware)
	.post("/", validator("json", uploadRequestSchema),
		describeRoute({
			summary: "Upload course image",
			tags: ["Courses"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { fileName, fileType, organizationId, fileData } = c.req.valid("json");

			if (user.role !== "admin") {
				return c.json({ error: "Only admins can upload images" }, 403);
			}

			// Verify user has permission in the organization
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can upload images" }, 403);
			}

			const fileExtension = fileName.split('.').pop();
			const timestamp = Date.now();
			const uniqueFileName = `${timestamp}_${Math.random().toString(36).substring(7)}.${fileExtension}`;
			const filePath = `courses/${organizationId}/${fileType}/${uniqueFileName}`;

			const bucket = process.env.S3_BUCKET_NAME;
			if (!bucket) {
				return c.json({ error: "Storage configuration missing" }, 500);
			}

			const getContentType = (fileName: string) => {
				const ext = fileName.split('.').pop()?.toLowerCase();
				switch (ext) {
					case 'jpg':
					case 'jpeg':
						return 'image/jpeg';
					case 'png':
						return 'image/png';
					case 'gif':
						return 'image/gif';
					case 'webp':
						return 'image/webp';
					case 'svg':
						return 'image/svg+xml';
					default:
						return 'image/jpeg';
				}
			};

			const contentType = getContentType(fileName);

			if (fileData) {
				try {
					const buffer = Buffer.from(fileData.split(',')[1], 'base64');

					await uploadFile(filePath, buffer, {
						bucket,
						contentType,
					});

					const accessUrl = await getSignedUrl(filePath, {
						bucket,
						expiresIn: 3600 * 24 * 7, // 7 days
					});

					return c.json({
						filePath,
						publicUrl: accessUrl,
					});
				} catch (uploadError) {
					console.error("Error uploading file:", uploadError);
					return c.json({ error: "Error uploading file" }, 500);
				}
			}

			const uploadUrl = await getSignedUploadUrl(filePath, {
				bucket,
				contentType
			});

			return c.json({
				uploadUrl,
				filePath,
				publicUrl: `${process.env.CDN_BASE_URL || process.env.S3_ENDPOINT}/${bucket}/${filePath}`,
			});
		} catch (error) {
			console.error("Error generating upload URL:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
