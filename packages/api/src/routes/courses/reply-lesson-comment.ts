import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  lessonId: z.string(),
  commentId: z.string(),
})

const bodySchema = z.object({
  content: z.string().min(1).max(1000),
})

export const replyLessonComment = new Hono()
	.use(authMiddleware)
	.post('/', validator('param', paramsSchema), validator('json', bodySchema),
		describeRoute({
			summary: "Reply to lesson comment",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const user = c.get('user')
      const { commentId } = c.req.valid('param')
      const { content } = c.req.valid('json')

      console.log('🔍 Comments API - Reply to comment:', { commentId, content, userId: user.id })

      // Check if comment exists
      const comment = await db.lessonComments.findUnique({
        where: { id: parseInt(commentId) },
        include: {
          lesson: {
            include: {
              module: {
                include: {
                  courseModules: {
                    include: {
                      course: {
                        include: {
                          organization: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!comment) {
        console.log('❌ Comments API - Comment not found:', commentId)
        return c.json({ error: 'Comment not found' }, 404)
      }

      // Create the reply in database
      const newReply = await db.lessonCommentReplies.create({
        data: {
          commentId: parseInt(commentId),
          userId: user.id,
          replyComment: content,
        },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      })

      const formattedReply = {
        id: newReply.id.toString(),
        content: newReply.replyComment,
        createdAt: newReply.createdAt.toISOString(),
        commentId: commentId,
        user: newReply.user
      }

      console.log('✅ Comments API - Reply added successfully')
      return c.json({ reply: formattedReply })
    } catch (error) {
      console.error('❌ Comments API - Error adding reply:', error)
      return c.json({ error: 'Failed to reply to comment' }, 500)
    }
  })
