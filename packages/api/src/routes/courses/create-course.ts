import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const createCourseSchema = z.object({
	name: z.string().min(1, "Course name is required"),
	description: z.string().optional(),
	organizationId: z.string(),
	community: z.string().optional(),
	link: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
		message: "Link must be a valid URL if provided",
	}),
	logo: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
		message: "Logo must be a valid URL if provided",
	}),
	modules: z
		.array(
			z.object({
				name: z.string().min(1, "Module name is required"),
				position: z.number().int().min(0),
				cover: z.string().optional().refine((val) => !val || z.string().url().safeParse(val).success, {
					message: "Cover must be a valid URL if provided",
				}),
			}),
		)
		.default([]),
});

export const createCourse = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.post("/",
		describeRoute({
			summary: "Create a new course",
			tags: ["Courses"],
		}),
		async (c) => {
		try {
			const user = c.get("user");

			let data;
			try {
				const rawData = await c.req.json();
				data = createCourseSchema.parse(rawData);
			} catch (error) {
				if (error instanceof z.ZodError) {
					return c.json({
						error: "Validation failed",
						details: error.errors.map(e => ({
							field: e.path.join('.'),
							message: e.message
						}))
					}, 400);
				}
				return c.json({ error: "Invalid request data" }, 400);
			}
			const { modules, organizationId, ...courseData } = data;

			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});



			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can create courses" }, 403);
			}

			const course = await db.courses.create({
				data: {
					...courseData,
					organizationId,
					createdBy: user.id,
				},
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
			});



			if (modules && modules.length > 0) {
				for (const moduleData of modules) {
					const module = await db.modules.create({
						data: {
							name: moduleData.name,
							position: moduleData.position,
							cover: moduleData.cover,
						},
					});

					await db.courseModules.create({
						data: {
							courseId: course.id,
							moduleId: module.id,
						},
					});
				}
			}

			return c.json(course);
		} catch (error) {
			console.error("Error creating course:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
