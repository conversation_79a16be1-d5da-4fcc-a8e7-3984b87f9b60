import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface WelcomeCourseAccessProps {
  name: string;
  courseName: string;
  courseId: string;
  productName: string;
  purchaseAmount: string;
  purchaseDate: string;
}

export const WelcomeCourseAccess = ({
  name,
  courseName,
  courseId,
  productName,
  purchaseAmount,
  purchaseDate,
}: WelcomeCourseAccessProps) => (
  <Wrapper>
    <Preview>🎉 Bem-vindo! Seu acesso ao curso {courseName} foi liberado</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 24, margin: "24px 0 8px", color: "#0F7864" }}>
        🎉 Parabéns! Seu acesso foi liberado!
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0", lineHeight: "1.6" }}>
        <PERSON><PERSON><PERSON> {name},<br />
        Sua compra do curso <strong>{courseName}</strong> foi processada com sucesso!<br />
        Seu acesso está liberado e você pode começar a estudar agora mesmo.
      </Text>

      <Section style={{
        background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        padding: "20px",
        borderRadius: "8px",
        margin: "24px 0",
        textAlign: "left",
        border: "1px solid #dee2e6"
      }}>
        <Text style={{ margin: "0 0 12px 0", fontSize: 16, fontWeight: "600", color: "#0F7864" }}>
          📋 Detalhes da sua compra:
        </Text>
        <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
          <strong>Produto:</strong> {productName}
        </Text>
        <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
          <strong>Curso:</strong> {courseName}
        </Text>
        <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
          <strong>Valor:</strong> {purchaseAmount}
        </Text>
        <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
          <strong>Data da compra:</strong> {purchaseDate}
        </Text>
      </Section>

      <Section style={{ margin: "32px 0" }}>
        <Link
          href={`${process.env.NEXT_PUBLIC_APP_URL || 'https://members.cakto.com.br'}/app/courses/${courseId}`}
          style={{
            display: "inline-block",
            background: "linear-gradient(135deg, #0F7864 0%, #0d6b5a 100%)",
            color: "#fff",
            padding: "14px 36px",
            borderRadius: "8px",
            fontWeight: "600",
            textDecoration: "none",
            fontSize: 16,
            boxShadow: "0 4px 12px rgba(15, 120, 100, 0.3)",
            transition: "all 0.3s ease"
          }}
        >
          🚀 Acessar Meu Curso
        </Link>
      </Section>

      <Section style={{
        background: "#d1ecf1",
        padding: "16px",
        borderRadius: "8px",
        margin: "24px 0",
        border: "1px solid #bee5eb"
      }}>
        <Text style={{ color: "#0c5460", fontSize: 14, margin: "0", textAlign: "left" }}>
          <strong>💡 Dica:</strong> Você receberá um link mágico por email para acessar diretamente o curso.
          Basta clicar no link para entrar automaticamente na plataforma.
        </Text>
      </Section>

      <Section style={{ margin: "32px 0", textAlign: "left" }}>
        <Text style={{ fontSize: 16, fontWeight: "600", color: "#0F7864", margin: "0 0 12px 0" }}>
          🎯 O que você encontrará no curso:
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Aulas em vídeo de alta qualidade
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Materiais complementares para download
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Comunidade exclusiva de alunos
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Suporte direto do instrutor
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Certificado de conclusão
        </Text>
      </Section>

      <Text style={{ color: "#6c757d", fontSize: 14, margin: "24px 0 0", fontStyle: "italic" }}>
        Bons estudos! Nossa equipe está aqui para apoiar seu aprendizado.<br />
        Se tiver dúvidas, não hesite em entrar em contato conosco.
      </Text>
    </Section>
  </Wrapper>
);

export default WelcomeCourseAccess;
