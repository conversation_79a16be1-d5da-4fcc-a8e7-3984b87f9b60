require('dotenv').config({ path: '.env.local' });
const { db } = require('./packages/database');

async function checkProducts() {
  try {
    const products = await db.courseProduct.findMany({
      include: {
        course: true
      }
    });
    
    console.log('Produtos cadastrados:');
    console.log(JSON.stringify(products, null, 2));
    
    if (products.length === 0) {
      console.log('\nNenhum produto encontrado. Vamos criar um produto de teste.');
      
      // Buscar um curso existente
      const courses = await db.courses.findMany();
      if (courses.length > 0) {
        const firstCourse = courses[0];
        console.log(`\nCriando produto para o curso: ${firstCourse.name}`);
        
        const newProduct = await db.courseProduct.create({
          data: {
            caktoProductId: 'cmdqkl4610002l504t2qxiqes',
            courseId: firstCourse.id,
            createdAt: new Date()
          },
          include: {
            course: true
          }
        });
        
        console.log('Produto criado:');
        console.log(JSON.stringify(newProduct, null, 2));
      } else {
        console.log('Nenhum curso encontrado para mapear.');
      }
    }
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await db.$disconnect();
  }
}

checkProducts();