import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config({ path: '.env.local' });

const prisma = new PrismaClient();

async function testAPIWithAuth() {
  try {
    console.log('🔍 Testando API com autenticação...');
    
    const user = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    
    if (!user) {
      console.log('❌ Usuário não encontrado');
      return;
    }
    
    console.log(`👤 Usuário: ${user.email}`);
    
    const session = await prisma.session.findFirst({
      where: {
        userId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!session) {
      console.log('❌ Nenhuma sessão encontrada para o usuário');
      console.log('🔄 Criando nova sessão...');
      
      const newSession = await prisma.session.create({
        data: {
          userId: user.id,
          token: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
      });
      
      console.log(`✅ Sessão criada: ${newSession.token}`);
      
      const response = await fetch('http://localhost:3000/api/vitrines/organization/vitrine?organizationSlug=cakto-members', {
        headers: {
          'Cookie': `better-auth.session_token=${newSession.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`📡 Status da resposta: ${response.status}`);
      
      if (response.status === 200) {
        const data = await response.json();
        console.log('✅ API funcionando! Dados recebidos:', JSON.stringify(data, null, 2));
      } else {
        const errorText = await response.text();
        console.log(`❌ Erro na API: ${errorText}`);
      }
      
    } else {
      console.log(`✅ Sessão encontrada: ${session.token}`);
      
      const response = await fetch('http://localhost:3000/api/vitrines/organization/vitrine?organizationSlug=cakto-members', {
        headers: {
          'Cookie': `better-auth.session_token=${session.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`📡 Status da resposta: ${response.status}`);
      
      if (response.status === 200) {
        const data = await response.json();
        console.log('✅ API funcionando! Dados recebidos:', JSON.stringify(data, null, 2));
      } else {
        const errorText = await response.text();
        console.log(`❌ Erro na API: ${errorText}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAPIWithAuth();