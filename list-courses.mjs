import 'dotenv/config';
import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function listCourses() {
  try {
    console.log('Listando cursos disponíveis...');
    
    const courses = await db.courses.findMany({
      include: {
        organization: {
          select: {
            name: true,
            slug: true
          }
        },
        caktoProducts: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    console.log(`\nTotal de cursos: ${courses.length}\n`);
    
    courses.forEach((course, index) => {
      console.log(`${index + 1}. ${course.name}`);
      console.log(`   ID: ${course.id}`);
      console.log(`   Organização: ${course.organization.name}`);
      console.log(`   Produtos Cakto associados: ${course.caktoProducts.length}`);
      
      if (course.caktoProducts.length > 0) {
        course.caktoProducts.forEach(product => {
          console.log(`     - ${product.caktoProductName || 'Sem nome'} (ID: ${product.caktoProductId})`);
        });
      }
      console.log('');
    });
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await db.$disconnect();
  }
}

listCourses();