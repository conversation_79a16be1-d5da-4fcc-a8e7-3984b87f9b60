import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config({ path: '.env.local' });

const prisma = new PrismaClient();

async function testBetterAuthSession() {
  try {
    console.log('🔍 Testando sessão do Better Auth...');
    
    const user = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    
    if (!user) {
      console.log('❌ Usuário não encontrado');
      return;
    }
    
    console.log(`👤 Usuário: ${user.email}`);
    
    const session = await prisma.session.findFirst({
      where: {
        userId: user.id,
        expiresAt: {
          gt: new Date()
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!session) {
      console.log('❌ Nenhuma sessão válida encontrada');
      return;
    }
    
    console.log(`✅ Sessão encontrada: ${session.token}`);
    
    console.log('\n🔍 Testando endpoint /api/auth/session...');
    
    const sessionResponse = await fetch('http://localhost:3000/api/auth/session', {
      headers: {
        'Cookie': `better-auth.session_token=${session.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Status: ${sessionResponse.status}`);
    
    if (sessionResponse.status === 200) {
      const sessionData = await sessionResponse.json();
      console.log('✅ Sessão válida:', JSON.stringify(sessionData, null, 2));
    } else {
      const errorText = await sessionResponse.text();
      console.log(`❌ Erro na sessão: ${errorText}`);
    }
    
    console.log('\n🔍 Testando endpoint /api/auth/get-session...');
    
    const getSessionResponse = await fetch('http://localhost:3000/api/auth/get-session', {
      headers: {
        'Cookie': `better-auth.session_token=${session.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Status: ${getSessionResponse.status}`);
    
    if (getSessionResponse.status === 200) {
      const getSessionData = await getSessionResponse.json();
      console.log('✅ Get session válido:', JSON.stringify(getSessionData, null, 2));
    } else {
      const errorText = await getSessionResponse.text();
      console.log(`❌ Erro no get session: ${errorText}`);
    }
    
    console.log('\n🔍 Testando com diferentes formatos de cookie...');
    
    const cookieVariations = [
      `better-auth.session_token=${session.token}`,
      `better-auth.session-token=${session.token}`,
      `session_token=${session.token}`,
      `authjs.session-token=${session.token}`
    ];
    
    for (const cookie of cookieVariations) {
      console.log(`\n🧪 Testando cookie: ${cookie}`);
      
      const testResponse = await fetch('http://localhost:3000/api/auth/session', {
        headers: {
          'Cookie': cookie,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`   Status: ${testResponse.status}`);
      
      if (testResponse.status === 200) {
        const data = await testResponse.json();
        console.log('   ✅ Sucesso!');
        break;
      }
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testBetterAuthSession();