import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

dotenv.config({ path: '.env.local' });

const prisma = new PrismaClient();

async function testAuthWithLogs() {
  try {
    console.log('🔍 Testando autenticação com logs detalhados...');
    
    const user = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    
    if (!user) {
      console.log('❌ Usuário não encontrado');
      return;
    }
    
    console.log(`👤 Usuário: ${user.email}`);
    
    const session = await prisma.session.findFirst({
      where: {
        userId: user.id,
        expiresAt: {
          gt: new Date()
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!session) {
      console.log('❌ Nenhuma sessão válida encontrada');
      return;
    }
    
    console.log(`✅ Sessão encontrada: ${session.token}`);
    console.log(`   Expira em: ${session.expiresAt}`);
    console.log(`   User ID: ${session.userId}`);
    
    console.log('\n🔍 Fazendo requisição para API de vitrines...');
    console.log('   (Verifique os logs do servidor para detalhes da autenticação)');
    
    const response = await fetch('http://localhost:3000/api/vitrines/organization/vitrine?organizationSlug=cakto-members', {
      headers: {
        'Cookie': `better-auth.session_token=${session.token}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Test Script',
        'Accept': 'application/json'
      }
    });
    
    console.log(`\n📡 Resposta da API:`);
    console.log(`   Status: ${response.status}`);
    console.log(`   Status Text: ${response.statusText}`);
    
    const responseHeaders = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });
    console.log(`   Headers:`, responseHeaders);
    
    if (response.status === 200) {
      const data = await response.json();
      console.log('\n✅ Sucesso! Dados recebidos:');
      console.log(JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log(`\n❌ Erro:`);
      console.log(errorText);
    }
    
    console.log('\n🔍 Testando endpoint de sessão do Better Auth...');
    
    const sessionResponse = await fetch('http://localhost:3000/api/auth/get-session', {
      headers: {
        'Cookie': `better-auth.session_token=${session.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`\n📡 Resposta do endpoint de sessão:`);
    console.log(`   Status: ${sessionResponse.status}`);
    
    if (sessionResponse.status === 200) {
      const sessionData = await sessionResponse.json();
      console.log('   Dados da sessão:', JSON.stringify(sessionData, null, 2));
    } else {
      const sessionError = await sessionResponse.text();
      console.log(`   Erro: ${sessionError}`);
    }
    
    console.log('\n🔍 Verificando dados da sessão no banco...');
    
    const dbSession = await prisma.session.findUnique({
      where: {
        token: session.token
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true
          }
        }
      }
    });
    
    if (dbSession) {
      console.log('✅ Sessão no banco:');
      console.log(`   Token: ${dbSession.token}`);
      console.log(`   User: ${dbSession.user.email}`);
      console.log(`   Role: ${dbSession.user.role}`);
      console.log(`   Expires: ${dbSession.expiresAt}`);
      console.log(`   Is expired: ${new Date() > dbSession.expiresAt}`);
      console.log(`   IP Address: ${dbSession.ipAddress}`);
      console.log(`   User Agent: ${dbSession.userAgent}`);
    } else {
      console.log('❌ Sessão não encontrada no banco');
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuthWithLogs();