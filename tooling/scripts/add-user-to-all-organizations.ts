#!/usr/bin/env tsx

import { db } from "@repo/database";

const USER_ID = "cmdqkbrhs000cyoovcukryxzb"; // ID do usuário fornecido

async function addUserToAllOrganizations() {
  try {
    console.log("🔧 Adicionando usuário a todas as organizações com cursos...");
    console.log(`👤 Usuário ID: ${USER_ID}`);

    // Verificar se o usuário existe
    const user = await db.user.findUnique({
      where: { id: USER_ID }
    });

    if (!user) {
      console.error("❌ Usuário não encontrado!");
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.name} (${user.email})`);

    // Buscar todas as organizações que têm cursos
    const organizationsWithCourses = await db.organization.findMany({
      where: {
        courses: {
          some: {}
        }
      },
      include: {
        courses: {
          select: {
            id: true,
            name: true
          }
        },
        members: {
          where: {
            userId: USER_ID
          },
          select: {
            id: true,
            role: true
          }
        }
      }
    });

    console.log(`📊 Total de organizações com cursos: ${organizationsWithCourses.length}`);

    if (organizationsWithCourses.length === 0) {
      console.log("⚠️ Nenhuma organização com cursos encontrada!");
      return;
    }

    console.log("\n📋 Organizações encontradas:");
    organizationsWithCourses.forEach((org, index) => {
      console.log(`${index + 1}. ${org.name} (${org.slug})`);
      console.log(`   - Cursos: ${org.courses.length}`);
      console.log(`   - Já é membro: ${org.members.length > 0 ? 'Sim' : 'Não'}`);
      if (org.members.length > 0) {
        console.log(`   - Role: ${org.members[0].role}`);
      }
      console.log("");
    });

    // Adicionar usuário às organizações que não é membro
    const organizationsToAdd = organizationsWithCourses.filter(org => org.members.length === 0);

    if (organizationsToAdd.length === 0) {
      console.log("✅ Usuário já é membro de todas as organizações!");
      return;
    }

    console.log(`🔧 Adicionando usuário a ${organizationsToAdd.length} organizações...`);

    const addedMemberships = await Promise.all(
      organizationsToAdd.map(async (org) => {
        const membership = await db.member.create({
          data: {
            userId: USER_ID,
            organizationId: org.id,
            role: "member",
            createdAt: new Date()
          },
          include: {
            organization: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        });

        return membership;
      })
    );

    console.log("✅ Usuário adicionado com sucesso às organizações:");
    addedMemberships.forEach((membership, index) => {
      console.log(`${index + 1}. ${membership.organization.name} (${membership.organization.slug})`);
    });

    // Verificar total de organizações do usuário
    const userMemberships = await db.member.findMany({
      where: { userId: USER_ID },
      include: {
        organization: true
      }
    });

    console.log(`\n📊 Total de organizações do usuário: ${userMemberships.length}`);
    console.log("📋 Todas as organizações:");
    userMemberships.forEach((member, index) => {
      console.log(`${index + 1}. ${member.organization.name} (${member.organization.slug}) - ${member.role}`);
    });

  } catch (error) {
    console.error("❌ Erro ao adicionar usuário às organizações:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
addUserToAllOrganizations();
