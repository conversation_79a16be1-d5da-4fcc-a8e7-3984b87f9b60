{"dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/auth": "workspace:*", "@repo/utils": "workspace:*"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.15.30", "nanoid": "^5.1.5", "tsx": "^4.19.4"}, "name": "@repo/scripts", "private": true, "scripts": {"create:user": "dotenv -c -e ../../.env -- tsx ./src/create-user.ts", "create:users:batch": "dotenv -c -e ../../.env -- tsx ./src/create-users-batch.ts", "grant:course-access": "dotenv -c -e ../../.env -- tsx ./grant-course-access.ts", "test:user-courses": "dotenv -c -e ../../.env -- tsx ./test-user-courses.ts", "check:membership": "dotenv -c -e ../../.env -- tsx ./check-user-membership.ts", "add:to-organization": "dotenv -c -e ../../.env -- tsx ./add-user-to-organization.ts", "add:to-all-organizations": "dotenv -c -e ../../.env -- tsx ./add-user-to-all-organizations.ts", "test:course-api": "dotenv -c -e ../../.env -- tsx ./test-course-api.ts", "type-check": "tsc --noEmit"}, "version": "0.0.0"}