#!/usr/bin/env tsx

import { db } from "@repo/database";

const USER_ID = "cmdqkbrhs000cyoovcukryxzb"; // ID do usuário fornecido
const COURSE_ID = "cmdqkbxth001yyoov30ws2fgo"; // ID do curso
const ORGANIZATION_SLUG = "techcorp-premium"; // Slug da organização

async function testCourseAPI() {
  try {
    console.log("🧪 Testando API do curso...");
    console.log(`👤 Usuário ID: ${USER_ID}`);
    console.log(`📚 Curso ID: ${COURSE_ID}`);
    console.log(`🏢 Organização Slug: ${ORGANIZATION_SLUG}`);

    // Verificar se o usuário existe
    const user = await db.user.findUnique({
      where: { id: USER_ID }
    });

    if (!user) {
      console.error("❌ Usuário não encontrado!");
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.name} (${user.email}) - Role: ${user.role}`);

    // Verificar se a organização existe
    const organization = await db.organization.findFirst({
      where: { slug: ORGANIZATION_SLUG }
    });

    if (!organization) {
      console.error("❌ Organização não encontrada!");
      return;
    }

    console.log(`✅ Organização encontrada: ${organization.name} (${organization.slug})`);

    // Verificar se o usuário é membro da organização
    const userMembership = await db.member.findFirst({
      where: {
        userId: USER_ID,
        organizationId: organization.id
      }
    });

    if (!userMembership) {
      console.error("❌ Usuário não é membro da organização!");
      return;
    }

    console.log(`✅ Usuário é membro da organização - Role: ${userMembership.role}`);

    // Verificar se o curso existe
    const course = await db.courses.findFirst({
      where: {
        id: COURSE_ID,
        organizationId: organization.id
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          }
        },
        courseModules: {
          include: {
            module: {
              include: {
                lessons: {
                  orderBy: { position: 'asc' },
                  include: {
                    userWatchedLessons: {
                      where: { userId: user.id },
                      select: {
                        isCompleted: true,
                        currentTime: true,
                        duration: true,
                      }
                    }
                  }
                }
              }
            }
          },
          orderBy: { module: { position: 'asc' } }
        }
      }
    });

    if (!course) {
      console.error("❌ Curso não encontrado!");

      // Verificar se o curso existe em outra organização
      const anyCourse = await db.courses.findFirst({
        where: { id: COURSE_ID },
        select: { id: true, name: true, organizationId: true }
      });

      if (anyCourse) {
        console.log(`🔍 Curso existe mas em organização diferente: ${anyCourse.organizationId}`);
      } else {
        console.log("🔍 Curso não existe no banco de dados");
      }
      return;
    }

    console.log(`✅ Curso encontrado: ${course.name}`);
    console.log(`   - Organização: ${course.organization.name}`);
    console.log(`   - Módulos: ${course.courseModules.length}`);

    // Verificar se o usuário tem acesso ao curso
    const userCourseAccess = await db.userCourses.findFirst({
      where: {
        userId: user.id,
        courseId: course.id
      }
    });

    if (!userCourseAccess && user.role !== 'admin') {
      console.error("❌ Usuário não tem acesso ao curso!");
      return;
    }

    console.log(`✅ Usuário tem acesso ao curso`);

    // Simular a resposta da API
    const formattedCourse = {
      id: course.id,
      name: course.name,
      logo: course.logo,
      community: course.community,
      link: course.link,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
      organization: course.organization,
      modules: course.courseModules.map(cm => ({
        id: cm.module.id,
        name: cm.module.name,
        position: cm.module.position,
        cover: cm.module.cover,
        courseId: course.id,
        lessons: cm.module.lessons.map(lesson => ({
          id: lesson.id,
          name: lesson.name,
          description: lesson.description,
          videoUrl: lesson.videoUrl,
          position: lesson.position,
          moduleId: cm.module.id,
          thumbnail: lesson.thumbnail,
          duration: lesson.duration,
          externalLink: lesson.externalLink,
          userWatchedLessons: lesson.userWatchedLessons[0] ? {
            isCompleted: lesson.userWatchedLessons[0].isCompleted,
            currentTime: lesson.userWatchedLessons[0].currentTime,
            duration: lesson.userWatchedLessons[0].duration,
          } : undefined,
        }))
      }))
    };

    console.log("✅ Dados do curso formatados com sucesso!");
    console.log(`📊 Total de módulos: ${formattedCourse.modules.length}`);
    console.log(`📊 Total de aulas: ${formattedCourse.modules.reduce((acc, module) => acc + module.lessons.length, 0)}`);

    console.log("\n📋 Módulos:");
    formattedCourse.modules.forEach((module, index) => {
      console.log(`${index + 1}. ${module.name} (${module.lessons.length} aulas)`);
    });

  } catch (error) {
    console.error("❌ Erro ao testar API do curso:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
testCourseAPI();
