#!/usr/bin/env tsx

import { db } from "@repo/database";

const USER_ID = "cmdqkbrhs000cyoovcukryxzb"; // ID do usuário fornecido
const ORGANIZATION_SLUG = "techcorp-premium"; // Slug da organização

async function addUserToOrganization() {
  try {
    console.log("🔧 Adicionando usuário à organização...");
    console.log(`👤 Usuário ID: ${USER_ID}`);
    console.log(`🏢 Organização Slug: ${ORGANIZATION_SLUG}`);

    // Verificar se o usuário existe
    const user = await db.user.findUnique({
      where: { id: USER_ID }
    });

    if (!user) {
      console.error("❌ Usuário não encontrado!");
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.name} (${user.email})`);

    // Verificar se a organização existe
    const organization = await db.organization.findFirst({
      where: { slug: ORGANIZATION_SLUG }
    });

    if (!organization) {
      console.error("❌ Organização não encontrada!");
      return;
    }

    console.log(`✅ Organização encontrada: ${organization.name} (${organization.slug})`);

    // Verificar se o usuário já é membro
    const existingMembership = await db.member.findFirst({
      where: {
        userId: USER_ID,
        organizationId: organization.id
      }
    });

    if (existingMembership) {
      console.log(`✅ Usuário já é membro da organização "${ORGANIZATION_SLUG}"`);
      console.log(`   - Role: ${existingMembership.role}`);
      console.log(`   - Data de entrada: ${existingMembership.createdAt.toLocaleDateString('pt-BR')}`);
      return;
    }

    // Adicionar usuário como membro
    const membership = await db.member.create({
      data: {
        userId: USER_ID,
        organizationId: organization.id,
        role: "member",
        createdAt: new Date()
      },
      include: {
        organization: true,
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    console.log("✅ Usuário adicionado com sucesso!");
    console.log(`   - Organização: ${membership.organization.name}`);
    console.log(`   - Role: ${membership.role}`);
    console.log(`   - Data de entrada: ${membership.createdAt.toLocaleDateString('pt-BR')}`);

    // Verificar total de organizações do usuário
    const userMemberships = await db.member.findMany({
      where: { userId: USER_ID },
      include: {
        organization: true
      }
    });

    console.log(`\n📊 Total de organizações do usuário: ${userMemberships.length}`);
    console.log("📋 Organizações:");
    userMemberships.forEach((member, index) => {
      console.log(`${index + 1}. ${member.organization.name} (${member.organization.slug}) - ${member.role}`);
    });

  } catch (error) {
    console.error("❌ Erro ao adicionar usuário à organização:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
addUserToOrganization();
