#!/usr/bin/env tsx

import { db } from "@repo/database";

const USER_ID = "cmdqkbrhs000cyoovcukryxzb"; // ID do usuário fornecido
const ORGANIZATION_SLUG = "techcorp-premium"; // Slug da organização

async function checkUserMembership() {
  try {
    console.log("🔍 Verificando membros do usuário...");
    console.log(`👤 Usuário ID: ${USER_ID}`);
    console.log(`🏢 Organização Slug: ${ORGANIZATION_SLUG}`);

    // Verificar se o usuário existe
    const user = await db.user.findUnique({
      where: { id: USER_ID },
      include: {
        members: {
          include: {
            organization: true
          }
        }
      }
    });

    if (!user) {
      console.error("❌ Usuário não encontrado!");
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.name} (${user.email})`);
    console.log(`📋 Total de organizações: ${user.members.length}`);

    if (user.members.length === 0) {
      console.log("⚠️ Usuário não é membro de nenhuma organização!");
      return;
    }

    console.log("\n📋 Organizações do usuário:");
    user.members.forEach((member, index) => {
      console.log(`${index + 1}. ${member.organization.name} (${member.organization.slug})`);
      console.log(`   - Role: ${member.role}`);
      console.log(`   - Data de entrada: ${member.createdAt.toLocaleDateString('pt-BR')}`);
      console.log("");
    });

    // Verificar se é membro da organização específica
    const targetMembership = user.members.find(member => member.organization.slug === ORGANIZATION_SLUG);

    if (targetMembership) {
      console.log(`✅ Usuário É membro da organização "${ORGANIZATION_SLUG}"`);
      console.log(`   - Role: ${targetMembership.role}`);
      console.log(`   - Data de entrada: ${targetMembership.createdAt.toLocaleDateString('pt-BR')}`);
    } else {
      console.log(`❌ Usuário NÃO é membro da organização "${ORGANIZATION_SLUG}"`);

      // Verificar se a organização existe
      const organization = await db.organization.findFirst({
        where: { slug: ORGANIZATION_SLUG }
      });

      if (organization) {
        console.log(`🏢 Organização "${ORGANIZATION_SLUG}" existe`);
        console.log(`   - Nome: ${organization.name}`);
        console.log(`   - ID: ${organization.id}`);
      } else {
        console.log(`❌ Organização "${ORGANIZATION_SLUG}" não existe`);
      }
    }

  } catch (error) {
    console.error("❌ Erro ao verificar membros do usuário:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
checkUserMembership();
