#!/usr/bin/env tsx

import { db } from "@repo/database";

const USER_ID = "cmdqkbrhs000cyoovcukryxzb"; // ID do usuário fornecido

async function testUserCourses() {
  try {
    console.log("🧪 Testando cursos do usuário...");
    console.log(`👤 Usuário ID: ${USER_ID}`);

    // Verificar se o usuário existe
    const user = await db.user.findUnique({
      where: { id: USER_ID },
      include: {
        userCourses: {
          include: {
            course: {
              include: {
                organization: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      console.error("❌ Usuário não encontrado!");
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.name} (${user.email})`);
    console.log(`📚 Total de cursos: ${user.userCourses.length}`);

    if (user.userCourses.length === 0) {
      console.log("⚠️ Usuário não tem acesso a nenhum curso!");
      return;
    }

    console.log("\n📋 Cursos do usuário:");
    user.userCourses.forEach((userCourse, index) => {
      console.log(`${index + 1}. ${userCourse.course.name} (${userCourse.course.organization.name})`);
      console.log(`   - ID: ${userCourse.course.id}`);
      console.log(`   - Descrição: ${userCourse.course.description || "Sem descrição"}`);
      console.log(`   - Logo: ${userCourse.course.logo || "Sem logo"}`);
      console.log(`   - Data de acesso: ${userCourse.createdAt.toLocaleDateString('pt-BR')}`);
      console.log("");
    });

    // Simular a transformação que a API faz
    const formattedCourses = user.userCourses.map((userCourse) => ({
      id: userCourse.course.id,
      title: userCourse.course.name,
      description: userCourse.course.description || "Curso disponível na plataforma",
      type: "course" as const,
      image: userCourse.course.logo,
      isAccessible: true,
      organizationSlug: userCourse.course.organization.slug,
      courseId: userCourse.courseId,
      finalTime: userCourse.finalTime?.toISOString(),
      createdAt: userCourse.createdAt.toISOString(),
      updatedAt: userCourse.updatedAt.toISOString(),
    }));

    console.log("✅ Transformação dos dados simulada com sucesso!");
    console.log(`📊 Total de cursos formatados: ${formattedCourses.length}`);

  } catch (error) {
    console.error("❌ Erro ao testar cursos do usuário:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
testUserCourses();
