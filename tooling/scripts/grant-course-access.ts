#!/usr/bin/env tsx

import { db } from "@repo/database";

const USER_ID = "cmdqkbrhs000cyoovcukryxzb"; // ID do usuário fornecido

async function grantCourseAccess() {
  try {
    console.log("🎯 Iniciando script para dar acesso a cursos...");
    console.log(`👤 Usuário ID: ${USER_ID}`);

    // Verificar se o usuário existe
    const user = await db.user.findUnique({
      where: { id: USER_ID },
      include: {
        userCourses: {
          include: {
            course: {
              include: {
                organization: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      console.error("❌ Usuário não encontrado!");
      return;
    }

    console.log(`✅ Usuário encontrado: ${user.name} (${user.email})`);
    console.log(`📚 Cursos atuais: ${user.userCourses.length}`);

    // Buscar todos os cursos disponíveis
    const allCourses = await db.courses.findMany({
      include: {
        organization: true
      },
      orderBy: {
        name: "asc"
      }
    });

    console.log(`📖 Total de cursos disponíveis: ${allCourses.length}`);

    // Verificar quais cursos o usuário já tem acesso
    const userCourseIds = user.userCourses.map(uc => uc.courseId);
    const coursesWithoutAccess = allCourses.filter(course => !userCourseIds.includes(course.id));

    console.log(`🔒 Cursos sem acesso: ${coursesWithoutAccess.length}`);

    if (coursesWithoutAccess.length === 0) {
      console.log("✅ Usuário já tem acesso a todos os cursos!");
      return;
    }

    // Dar acesso aos primeiros 5 cursos que o usuário não tem
    const coursesToGrant = coursesWithoutAccess.slice(0, 5);

    console.log(`🎁 Concedendo acesso a ${coursesToGrant.length} cursos...`);

    const grantedAccess = await Promise.all(
      coursesToGrant.map(async (course) => {
        const userCourse = await db.userCourses.create({
          data: {
            userId: USER_ID,
            courseId: course.id,
            finalTime: null, // Sem data de expiração
          },
          include: {
            course: {
              include: {
                organization: true
              }
            }
          }
        });

        return userCourse;
      })
    );

    console.log("✅ Acesso concedido com sucesso!");
    console.log("\n📋 Cursos adicionados:");

    grantedAccess.forEach((access, index) => {
      console.log(`${index + 1}. ${access.course.name} (${access.course.organization.name})`);
    });

    // Verificar total de cursos após a operação
    const updatedUser = await db.user.findUnique({
      where: { id: USER_ID },
      include: {
        userCourses: {
          include: {
            course: {
              include: {
                organization: true
              }
            }
          }
        }
      }
    });

    console.log(`\n📊 Total de cursos após operação: ${updatedUser?.userCourses.length}`);

  } catch (error) {
    console.error("❌ Erro ao executar script:", error);
  } finally {
    await db.$disconnect();
  }
}

// Executar o script
grantCourseAccess();
