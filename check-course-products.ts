import 'dotenv/config';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkCourseProducts() {
  try {
    console.log('Verificando produtos cadastrados...');
    
    const products = await prisma.courseProduct.findMany({
      include: {
        course: true
      }
    });
    
    console.log(`Total de produtos: ${products.length}`);
    
    if (products.length > 0) {
      console.log('\nProdutos encontrados:');
      products.forEach(product => {
        console.log(`- ID: ${product.caktoProductId}`);
        console.log(`  Curso: ${product.course.name}`);
        console.log(`  Nome do Produto: ${product.caktoProductName || 'N/A'}`);
        console.log('');
      });
      
      const testProductExists = products.find(p => p.caktoProductId === 'a6266a5d-bfae-4561-a181-fc8fc17a3981');
      if (!testProductExists) {
        console.log('\nCriando produto de teste para webhook...');
        const courses = await prisma.courses.findMany();
        if (courses.length > 0) {
          const firstCourse = courses[0];
          const newProduct = await prisma.courseProduct.create({
            data: {
              caktoProductId: 'a6266a5d-bfae-4561-a181-fc8fc17a3981',
              courseId: firstCourse.id,
              caktoProductName: 'Como vender na Cakto'
            },
            include: {
              course: true
            }
          });
          console.log('Produto de teste criado:');
          console.log(`- ID: ${newProduct.caktoProductId}`);
          console.log(`- Curso: ${newProduct.course.name}`);
        }
      }
    } else {
      console.log('\nNenhum produto encontrado. Criando produto de teste...');
      
      const courses = await prisma.courses.findMany();
      if (courses.length > 0) {
        const firstCourse = courses[0];
        console.log(`Criando produto para o curso: ${firstCourse.name}`);
        
        const newProduct = await prisma.courseProduct.create({
          data: {
            caktoProductId: 'a6266a5d-bfae-4561-a181-fc8fc17a3981',
            courseId: firstCourse.id,
            caktoProductName: 'Como vender na Cakto'
          },
          include: {
            course: true
          }
        });
        
        console.log('Produto criado:');
        console.log(`- ID: ${newProduct.caktoProductId}`);
        console.log(`- Curso: ${newProduct.course.name}`);
      } else {
        console.log('Nenhum curso encontrado.');
      }
    }
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCourseProducts();