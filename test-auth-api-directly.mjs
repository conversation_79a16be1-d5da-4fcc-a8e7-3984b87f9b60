import { auth } from '@repo/auth';
import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const prisma = new PrismaClient();

async function testAuthAPIDirect() {
  try {
    console.log('🔍 Testando auth.api.getSession diretamente...');
    
    const user = await prisma.user.findFirst({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        email: true,
        name: true
      }
    });
    
    if (!user) {
      console.log('❌ Usuário não encontrado');
      return;
    }
    
    console.log(`👤 Usuário: ${user.email}`);
    
    const session = await prisma.session.findFirst({
      where: {
        userId: user.id,
        expiresAt: {
          gt: new Date()
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!session) {
      console.log('❌ Nenhuma sessão válida encontrada');
      return;
    }
    
    console.log(`✅ Sessão encontrada: ${session.token}`);
    
    // Simular headers de requisição
    const mockHeaders = new Headers();
    mockHeaders.set('cookie', `better-auth.session_token=${session.token}`);
    mockHeaders.set('content-type', 'application/json');
    
    console.log('\n🔍 Testando auth.api.getSession com headers simulados...');
    
    try {
      const authSession = await auth.api.getSession({
        headers: mockHeaders
      });
      
      if (authSession) {
        console.log('✅ Sessão válida encontrada pelo Better Auth!');
        console.log('   User ID:', authSession.user.id);
        console.log('   User Email:', authSession.user.email);
        console.log('   Session ID:', authSession.session.id);
        console.log('   Session Token:', authSession.session.token.slice(-4));
      } else {
        console.log('❌ Nenhuma sessão encontrada pelo Better Auth');
      }
    } catch (error) {
      console.error('❌ Erro ao chamar auth.api.getSession:', error);
    }
    
    console.log('\n🔍 Testando diferentes formatos de cookie...');
    
    const cookieFormats = [
      `better-auth.session_token=${session.token}`,
      `better-auth.session-token=${session.token}`,
      `session_token=${session.token}`,
      `authjs.session-token=${session.token}`,
      `next-auth.session-token=${session.token}`
    ];
    
    for (const cookieFormat of cookieFormats) {
      console.log(`\n🧪 Testando formato: ${cookieFormat}`);
      
      const testHeaders = new Headers();
      testHeaders.set('cookie', cookieFormat);
      testHeaders.set('content-type', 'application/json');
      
      try {
        const testSession = await auth.api.getSession({
          headers: testHeaders
        });
        
        if (testSession) {
          console.log('   ✅ Sucesso! Sessão encontrada');
          console.log('   User:', testSession.user.email);
          break;
        } else {
          console.log('   ❌ Nenhuma sessão encontrada');
        }
      } catch (error) {
        console.log('   ❌ Erro:', error.message);
      }
    }
    
    console.log('\n🔍 Verificando configuração do Better Auth...');
    
    // Verificar se o auth está configurado corretamente
    console.log('Auth configurado:', !!auth);
    console.log('Auth.api disponível:', !!auth.api);
    console.log('Auth.api.getSession disponível:', !!auth.api.getSession);
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testAuthAPIDirect();